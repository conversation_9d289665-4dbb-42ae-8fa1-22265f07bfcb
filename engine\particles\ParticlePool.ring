/*
الكلاس: ParticlePool
الوصف: مجمع جسيمات لتحسين الأداء وإدارة الذاكرة
المدخلات: حجم المجمع
المخرجات: إدارة فعالة للجسيمات
*/

//load "Particle.ring"

class ParticlePool {
    
    func init nPoolSize    # = 1000
        nMaxSize = nPoolSize
        aAvailableParticles = []
        aUsedParticles = []
        nCurrentSize = 0
        
        # إنشاء الجسيمات المسبقة
        initializePool()
        
        # إحصائيات المجمع
        nParticlesCreated = 0
        nParticlesReused = 0
        nPoolHits = 0
        nPoolMisses = 0

    func initializePool
        # إنشاء جسيمات مسبقة في المجمع
        for i = 1 to nMaxSize {
            oParticle = new Particle()
            add(aAvailableParticles, oParticle)
            nCurrentSize++
        }
        
        ? "تم إنشاء مجمع جسيمات بحجم: " + string(nMaxSize)

    func getParticle
        # الحصول على جسيمة من المجمع
        if len(aAvailableParticles) > 0 {
            # أخذ جسيمة من المجمع
            oParticle = aAvailableParticles[1]
            del(aAvailableParticles, 1)
            add(aUsedParticles, oParticle)
            
            # إعادة تعيين الجسيمة
            oParticle.reset()
            
            nPoolHits++
            nParticlesReused++
            return oParticle
        else
            # المجمع فارغ
            nPoolMisses++
            return null
        }

    func returnParticle oParticle
        # إرجاع جسيمة للمجمع
        if oParticle = null {
            return false
        }
        
        # البحث عن الجسيمة في قائمة المستخدمة
        nIndex = find(aUsedParticles, oParticle)
        if nIndex > 0 {
            # إزالة من قائمة المستخدمة
            del(aUsedParticles, nIndex)
            
            # إضافة لقائمة المتاحة
            add(aAvailableParticles, oParticle)
            
            # إعادة تعيين الجسيمة
            oParticle.reset()
            
            return true
        }
        
        return false

    func canAcceptParticle oParticle
        # فحص إمكانية قبول الجسيمة في المجمع
        # يمكن تخصيص هذا حسب نوع الجسيمة
        return true

    func expandPool nAdditionalSize
        # توسيع المجمع بإضافة جسيمات جديدة
        nOldSize = nMaxSize
        nMaxSize += nAdditionalSize
        
        for i = 1 to nAdditionalSize {
            oParticle = new Particle()
            add(aAvailableParticles, oParticle)
            nCurrentSize++
            nParticlesCreated++
        }
        
        ? "تم توسيع المجمع من " + string(nOldSize) + " إلى " + string(nMaxSize)

    func shrinkPool nReduceSize
        # تقليص المجمع بإزالة جسيمات غير مستخدمة
        nToRemove = min(nReduceSize, len(aAvailableParticles))
        
        for i = 1 to nToRemove {
            if len(aAvailableParticles) > 0 {
                del(aAvailableParticles, 1)
                nCurrentSize--
            }
        }
        
        nMaxSize -= nToRemove
        ? "تم تقليص المجمع بمقدار " + string(nToRemove) + " جسيمة"

    func getAvailableCount
        return len(aAvailableParticles)

    func getUsedCount
        return len(aUsedParticles)

    func getTotalCount
        return nCurrentSize

    func getMaxSize
        return nMaxSize

    func getUtilization
        # حساب نسبة الاستخدام
        if nMaxSize = 0 {
            return 0.0
        }
        return len(aUsedParticles) / nMaxSize

    func getHitRate
        # حساب معدل النجاح في المجمع
        nTotalRequests = nPoolHits + nPoolMisses
        if nTotalRequests = 0 {
            return 0.0
        }
        return nPoolHits / nTotalRequests

    func getStatistics
        # إرجاع إحصائيات المجمع
        return [
            :maxSize = nMaxSize,
            :currentSize = nCurrentSize,
            :available = len(aAvailableParticles),
            :used = len(aUsedParticles),
            :utilization = getUtilization(),
            :hitRate = getHitRate(),
            :particlesCreated = nParticlesCreated,
            :particlesReused = nParticlesReused,
            :poolHits = nPoolHits,
            :poolMisses = nPoolMisses
        ]

    func printStatistics
        # طباعة إحصائيات المجمع
        ? "=== إحصائيات مجمع الجسيمات ==="
        ? "الحجم الأقصى: " + string(nMaxSize)
        ? "الحجم الحالي: " + string(nCurrentSize)
        ? "المتاحة: " + string(len(aAvailableParticles))
        ? "المستخدمة: " + string(len(aUsedParticles))
        ? "نسبة الاستخدام: " + string(getUtilization() * 100) + "%"
        ? "معدل النجاح: " + string(getHitRate() * 100) + "%"
        ? "الجسيمات المنشأة: " + string(nParticlesCreated)
        ? "الجسيمات المعاد استخدامها: " + string(nParticlesReused)
        ? "نجاحات المجمع: " + string(nPoolHits)
        ? "إخفاقات المجمع: " + string(nPoolMisses)

    func optimizePool
        # تحسين المجمع بناءً على الاستخدام
        nUtilization = getUtilization()
        
        if nUtilization > 0.9 {
            # المجمع مكتظ، قم بتوسيعه
            nExpansion = nMaxSize * 0.2  # زيادة 20%
            expandPool(nExpansion)
        elseif nUtilization < 0.3 and nMaxSize > 100
            # المجمع كبير جداً، قم بتقليصه
            nReduction = nMaxSize * 0.1  # تقليل 10%
            shrinkPool(nReduction)
        }

    func resetStatistics
        # إعادة تعيين الإحصائيات
        nParticlesCreated = 0
        nParticlesReused = 0
        nPoolHits = 0
        nPoolMisses = 0

    func cleanup
        # تنظيف المجمع
        aAvailableParticles = []
        aUsedParticles = []
        nCurrentSize = 0
        nMaxSize = 0
        
        ? "تم تنظيف مجمع الجسيمات"

    func min nA, nB
        if nA < nB {
            return nA
        }
        return nB

    private
        nMaxSize
        nCurrentSize
        aAvailableParticles
        aUsedParticles
        nParticlesCreated
        nParticlesReused
        nPoolHits
        nPoolMisses
}
