/*
الكلاس: SphereCollider
الوصف: كاشف تصادم كروي الشكل
المدخلات: نصف قطر وموقع الكرة
المخرجات: كشف التصادمات مع الكائنات الأخرى
*/

//load "../core/Component.ring"

class SphereCollider from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("SphereCollider")
        
        # خصائص الكرة
        nRadius = 0.5
        aCenter = [0.0, 0.0, 0.0]
        
        # خصائص الفيزياء
        bIsTrigger = false
        bIsStatic = false
        nMass = 1.0
        nRestitution = 0.5  # الارتداد
        nFriction = 0.7     # الاحتكاك
        
        # حالة التصادم
        bIsColliding = false
        aCollidingObjects = []
        
        # مادة الفيزياء
        oPhysicsMaterial = null
        
        # الحدود المحسوبة
        aBoundsMin = [0, 0, 0]
        aBoundsMax = [0, 0, 0]
        aWorldCenter = [0, 0, 0]
        nWorldRadius = 0.5

    func setRadius nNewRadius
        nRadius = nNewRadius
        updateBounds()

    func getRadius
        return nRadius

    func setCenter aNewCenter
        aCenter = aNewCenter
        updateBounds()

    func getCenter
        return aCenter

    func setTrigger bState
        bIsTrigger = bState

    func isTrigger
        return bIsTrigger

    func setStatic bState
        bIsStatic = bState

    func isStatic
        return bIsStatic

    func setMass nNewMass
        nMass = nNewMass

    func getMass
        return nMass

    func setRestitution nValue
        nRestitution = nValue

    func getRestitution
        return nRestitution

    func setFriction nValue
        nFriction = nValue

    func getFriction
        return nFriction

    func setPhysicsMaterial oMaterial
        oPhysicsMaterial = oMaterial

    func getPhysicsMaterial
        return oPhysicsMaterial

    func updateBounds
        # تحديث حدود الكرة
        if oGameObject != null {
            aWorldPos = oGameObject.getPosition()
            aWorldScale = oGameObject.getScale()
            
            # حساب المركز في الإحداثيات العالمية
            aWorldCenter = [
                aWorldPos[1] + aCenter[1],
                aWorldPos[2] + aCenter[2],
                aWorldPos[3] + aCenter[3]
            ]
            
            # حساب نصف القطر مع مراعاة التحجيم
            nMaxScale = max(aWorldScale[1], max(aWorldScale[2], aWorldScale[3]))
            nWorldRadius = nRadius * nMaxScale
            
            # حساب الحدود المكعبة للكرة
            aBoundsMin = [
                aWorldCenter[1] - nWorldRadius,
                aWorldCenter[2] - nWorldRadius,
                aWorldCenter[3] - nWorldRadius
            ]
            
            aBoundsMax = [
                aWorldCenter[1] + nWorldRadius,
                aWorldCenter[2] + nWorldRadius,
                aWorldCenter[3] + nWorldRadius
            ]
        }

    func getBounds
        return [aBoundsMin, aBoundsMax]

    func getBoundsMin
        return aBoundsMin

    func getBoundsMax
        return aBoundsMax

    func getWorldCenter
        return aWorldCenter

    func getWorldRadius
        return nWorldRadius

    func checkCollision oOtherCollider
        # فحص التصادم مع كاشف آخر
        if oOtherCollider = null {
            return false
        }
        
        # تحديث الحدود
        updateBounds()
        oOtherCollider.updateBounds()
        
        # فحص نوع الكاشف الآخر
        cOtherShape = oOtherCollider.getShape()
        
        if cOtherShape = "sphere" {
            return checkSphereToSphere(oOtherCollider)
        elseif cOtherShape = "box"
            return checkSphereToBox(oOtherCollider)
        else
            # فحص AABB عام
            return checkAABB(oOtherCollider)
        }

    func checkSphereToSphere oOtherSphere
        # فحص التصادم بين كرتين
        aOtherCenter = oOtherSphere.getWorldCenter()
        nOtherRadius = oOtherSphere.getWorldRadius()
        
        # حساب المسافة بين المراكز
        nDx = aWorldCenter[1] - aOtherCenter[1]
        nDy = aWorldCenter[2] - aOtherCenter[2]
        nDz = aWorldCenter[3] - aOtherCenter[3]
        nDistance = sqrt(nDx*nDx + nDy*nDy + nDz*nDz)
        
        # فحص التداخل
        return nDistance <= (nWorldRadius + nOtherRadius)

    func checkSphereToBox oBoxCollider
        # فحص التصادم بين كرة ومكعب
        aBoxMin = oBoxCollider.getBoundsMin()
        aBoxMax = oBoxCollider.getBoundsMax()
        
        # العثور على أقرب نقطة في المكعب للكرة
        nClosestX = max(aBoxMin[1], min(aWorldCenter[1], aBoxMax[1]))
        nClosestY = max(aBoxMin[2], min(aWorldCenter[2], aBoxMax[2]))
        nClosestZ = max(aBoxMin[3], min(aWorldCenter[3], aBoxMax[3]))
        
        # حساب المسافة من مركز الكرة لأقرب نقطة
        nDx = aWorldCenter[1] - nClosestX
        nDy = aWorldCenter[2] - nClosestY
        nDz = aWorldCenter[3] - nClosestZ
        nDistance = sqrt(nDx*nDx + nDy*nDy + nDz*nDz)
        
        return nDistance <= nWorldRadius

    func checkAABB oOtherCollider
        # فحص AABB عام
        aOtherMin = oOtherCollider.getBoundsMin()
        aOtherMax = oOtherCollider.getBoundsMax()
        
        # فحص التداخل في كل محور
        bOverlapX = aBoundsMax[1] >= aOtherMin[1] and aBoundsMin[1] <= aOtherMax[1]
        bOverlapY = aBoundsMax[2] >= aOtherMin[2] and aBoundsMin[2] <= aOtherMax[2]
        bOverlapZ = aBoundsMax[3] >= aOtherMin[3] and aBoundsMin[3] <= aOtherMax[3]
        
        return bOverlapX and bOverlapY and bOverlapZ

    func getContactPoints oOtherCollider
        # حساب نقاط التلامس
        aContactPoints = []
        
        if checkCollision(oOtherCollider) {
            cOtherShape = oOtherCollider.getShape()
            
            if cOtherShape = "sphere" {
                # نقطة التلامس بين كرتين
                aOtherCenter = oOtherCollider.getWorldCenter()
                
                # الاتجاه من هذه الكرة للأخرى
                nDx = aOtherCenter[1] - aWorldCenter[1]
                nDy = aOtherCenter[2] - aWorldCenter[2]
                nDz = aOtherCenter[3] - aWorldCenter[3]
                nDistance = sqrt(nDx*nDx + nDy*nDy + nDz*nDz)
                
                if nDistance > 0 {
                    # تطبيع الاتجاه
                    nDx = nDx / nDistance
                    nDy = nDy / nDistance
                    nDz = nDz / nDistance
                    
                    # نقطة التلامس على سطح هذه الكرة
                    aContactPoint = [
                        aWorldCenter[1] + nDx * nWorldRadius,
                        aWorldCenter[2] + nDy * nWorldRadius,
                        aWorldCenter[3] + nDz * nWorldRadius
                    ]
                    
                    add(aContactPoints, aContactPoint)
                }
            else
                # نقطة التلامس عامة (مركز التداخل)
                aOtherMin = oOtherCollider.getBoundsMin()
                aOtherMax = oOtherCollider.getBoundsMax()
                
                nContactX = (max(aBoundsMin[1], aOtherMin[1]) + min(aBoundsMax[1], aOtherMax[1])) / 2.0
                nContactY = (max(aBoundsMin[2], aOtherMin[2]) + min(aBoundsMax[2], aOtherMax[2])) / 2.0
                nContactZ = (max(aBoundsMin[3], aOtherMin[3]) + min(aBoundsMax[3], aOtherMax[3])) / 2.0
                
                add(aContactPoints, [nContactX, nContactY, nContactZ])
            }
        }
        
        return aContactPoints

    func getCollisionNormal oOtherCollider
        # حساب اتجاه التصادم
        if not checkCollision(oOtherCollider) {
            return [0, 0, 0]
        }
        
        cOtherShape = oOtherCollider.getShape()
        
        if cOtherShape = "sphere" {
            # الاتجاه بين مراكز الكرتين
            aOtherCenter = oOtherCollider.getWorldCenter()
            
            nDx = aWorldCenter[1] - aOtherCenter[1]
            nDy = aWorldCenter[2] - aOtherCenter[2]
            nDz = aWorldCenter[3] - aOtherCenter[3]
            nDistance = sqrt(nDx*nDx + nDy*nDy + nDz*nDz)
            
            if nDistance > 0 {
                return [nDx/nDistance, nDy/nDistance, nDz/nDistance]
            }
        else
            # اتجاه من مركز الكرة لأقرب نقطة في الكاشف الآخر
            aOtherMin = oOtherCollider.getBoundsMin()
            aOtherMax = oOtherCollider.getBoundsMax()
            
            nClosestX = max(aOtherMin[1], min(aWorldCenter[1], aOtherMax[1]))
            nClosestY = max(aOtherMin[2], min(aWorldCenter[2], aOtherMax[2]))
            nClosestZ = max(aOtherMin[3], min(aWorldCenter[3], aOtherMax[3]))
            
            nDx = aWorldCenter[1] - nClosestX
            nDy = aWorldCenter[2] - nClosestY
            nDz = aWorldCenter[3] - nClosestZ
            nDistance = sqrt(nDx*nDx + nDy*nDy + nDz*nDz)
            
            if nDistance > 0 {
                return [nDx/nDistance, nDy/nDistance, nDz/nDistance]
            }
        }
        
        return [0, 1, 0]  # اتجاه افتراضي

    func onCollisionEnter oOtherCollider
        # استدعى عند بداية التصادم
        if not find(aCollidingObjects, oOtherCollider) {
            add(aCollidingObjects, oOtherCollider)
            bIsColliding = true
            
            # إشعار الكائن المالك
            if oGameObject != null {
                oGameObject.onCollisionEnter(this, oOtherCollider)
            }
        }

    func onCollisionExit oOtherCollider
        # استدعى عند انتهاء التصادم
        nIndex = find(aCollidingObjects, oOtherCollider)
        if nIndex > 0 {
            del(aCollidingObjects, nIndex)
            
            if len(aCollidingObjects) = 0 {
                bIsColliding = false
            }
            
            # إشعار الكائن المالك
            if oGameObject != null {
                oGameObject.onCollisionExit(this, oOtherCollider)
            }
        }

    func update nDeltaTime
        # تحديث المكون
        updateBounds()

    func isColliding
        return bIsColliding

    func getCollidingObjects
        return aCollidingObjects

    func getShape
        return "sphere"

    private
        nRadius
        aCenter
        bIsTrigger
        bIsStatic
        nMass
        nRestitution
        nFriction
        bIsColliding
        aCollidingObjects
        oPhysicsMaterial
        aBoundsMin
        aBoundsMax
        aWorldCenter
        nWorldRadius
}
