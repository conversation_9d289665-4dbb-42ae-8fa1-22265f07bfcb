/*
Ring Game Engine - Main Entry Point
الوصف: نقطة الدخول الرئيسية لمحرك الألعاب Ring Game Engine
المدخلات: لا يوجد
المخرجات: تحميل جميع مكونات المحرك
*/

# تحميل مكتبة RayLib الأساسية
load "raylib.ring"

# تحميل المكونات الأساسية للمحرك
load "engine/core/Engine.ring"
load "engine/core/GameObject.ring"
load "engine/core/Component.ring"
load "engine/core/Camera.ring"
load "engine/core/Light3D.ring"
load "engine/core/Material.ring"
load "engine/core/Mesh.ring"
load "engine/core/MeshRenderer.ring"
load "engine/core/Renderer.ring"
load "engine/core/Resource.ring"
load "engine/core/ResourceManager.ring"
load "engine/core/WindowManager.ring"
load "engine/core/CameraFollowComponent.ring"

# تحميل أنظمة المشاهد
load "engine/scene/Scene.ring"
load "engine/scene/SceneGraph.ring"
load "engine/scene/SceneManager.ring"

# تحميل نظام المدخلات
load "engine/input/InputManager.ring"
load "engine/input/InputMapping.ring"
load "engine/input/PlayerController.ring"

# تحميل محرك الفيزياء
load "engine/physics/PhysicsEngine.ring"
load "engine/physics/AdvancedPhysics.ring"
load "engine/physics/RigidBody.ring"
load "engine/physics/BoxCollider.ring"
load "engine/physics/SphereCollider.ring"

# تحميل محرك الصوت
load "engine/audio/AudioEngine.ring"

# تحميل نظام الجسيمات
load "engine/particles/Particle.ring"
load "engine/particles/ParticleEmitter.ring"
load "engine/particles/ParticlePool.ring"
load "engine/particles/ParticleSystem.ring"

# تحميل نظام الذكاء الاصطناعي
load "engine/ai/AISystem.ring"
load "engine/ai/BehaviorTree.ring"
load "engine/ai/FiniteStateMachine.ring"
load "engine/ai/AIAgent.ring"

# تحميل نظام التحريك
load "engine/animation/AnimationSystem.ring"
load "engine/animation/RotateComponent.ring"

# تحميل أدوات التصحيح
load "engine/debug/DebugTools.ring"

# تحميل محرر المشاهد
load "engine/editor/EditorUI.ring"
load "engine/editor/LevelEditor.ring"
load "engine/editor/SceneEditor.ring"

# تحميل نظام الشبكات
load "engine/network/NetworkManager.ring"

# تحميل نظام النصوص البرمجية
load "engine/scripting/ScriptingSystem.ring"

# تحميل نظام القصة
load "engine/story/StorySystem.ring"

# تحميل نظام الإنجازات
load "engine/achievement/AchievementSystem.ring"

# إعداد الثوابت العامة
WHITE = [255, 255, 255, 255]
BLACK = [0, 0, 0, 255]
RED = [255, 0, 0, 255]
GREEN = [0, 255, 0, 255]
BLUE = [0, 0, 255, 255]
YELLOW = [255, 255, 0, 255]
MAGENTA = [255, 0, 255, 255]
CYAN = [0, 255, 255, 255]

# ثوابت أنواع الإضاءة
LIGHT_DIRECTIONAL = 0
LIGHT_POINT = 1
LIGHT_SPOT = 2

# ثوابت أنماط التحكم في الكاميرا
CAMERA_CONTROL_FREE = 0
CAMERA_CONTROL_ORBITAL = 1
CAMERA_CONTROL_FIRST_PERSON = 2
CAMERA_CONTROL_THIRD_PERSON = 3

# ثوابت المفاتيح (من RayLib)
KEY_SPACE = 32
KEY_ESCAPE = 256
KEY_ENTER = 257
KEY_TAB = 258
KEY_BACKSPACE = 259
KEY_INSERT = 260
KEY_DELETE = 261
KEY_RIGHT = 262
KEY_LEFT = 263
KEY_DOWN = 264
KEY_UP = 265
KEY_PAGE_UP = 266
KEY_PAGE_DOWN = 267
KEY_HOME = 268
KEY_END = 269
KEY_CAPS_LOCK = 280
KEY_SCROLL_LOCK = 281
KEY_NUM_LOCK = 282
KEY_PRINT_SCREEN = 283
KEY_PAUSE = 284
KEY_F1 = 290
KEY_F2 = 291
KEY_F3 = 292
KEY_F4 = 293
KEY_F5 = 294
KEY_F6 = 295
KEY_F7 = 296
KEY_F8 = 297
KEY_F9 = 298
KEY_F10 = 299
KEY_F11 = 300
KEY_F12 = 301
KEY_LEFT_SHIFT = 340
KEY_LEFT_CONTROL = 341
KEY_LEFT_ALT = 342
KEY_LEFT_SUPER = 343
KEY_RIGHT_SHIFT = 344
KEY_RIGHT_CONTROL = 345
KEY_RIGHT_ALT = 346
KEY_RIGHT_SUPER = 347
KEY_KB_MENU = 348
KEY_LEFT_BRACKET = 91
KEY_BACKSLASH = 92
KEY_RIGHT_BRACKET = 93
KEY_GRAVE = 96
KEY_KP_0 = 320
KEY_KP_1 = 321
KEY_KP_2 = 322
KEY_KP_3 = 323
KEY_KP_4 = 324
KEY_KP_5 = 325
KEY_KP_6 = 326
KEY_KP_7 = 327
KEY_KP_8 = 328
KEY_KP_9 = 329
KEY_KP_DECIMAL = 330
KEY_KP_DIVIDE = 331
KEY_KP_MULTIPLY = 332
KEY_KP_SUBTRACT = 333
KEY_KP_ADD = 334
KEY_KP_ENTER = 335
KEY_KP_EQUAL = 336
KEY_APOSTROPHE = 39
KEY_COMMA = 44
KEY_MINUS = 45
KEY_PERIOD = 46
KEY_SLASH = 47
KEY_ZERO = 48
KEY_ONE = 49
KEY_TWO = 50
KEY_THREE = 51
KEY_FOUR = 52
KEY_FIVE = 53
KEY_SIX = 54
KEY_SEVEN = 55
KEY_EIGHT = 56
KEY_NINE = 57
KEY_SEMICOLON = 59
KEY_EQUAL = 61
KEY_A = 65
KEY_B = 66
KEY_C = 67
KEY_D = 68
KEY_E = 69
KEY_F = 70
KEY_G = 71
KEY_H = 72
KEY_I = 73
KEY_J = 74
KEY_K = 75
KEY_L = 76
KEY_M = 77
KEY_N = 78
KEY_O = 79
KEY_P = 80
KEY_Q = 81
KEY_R = 82
KEY_S = 83
KEY_T = 84
KEY_U = 85
KEY_V = 86
KEY_W = 87
KEY_X = 88
KEY_Y = 89
KEY_Z = 90

# دالة مساعدة لإنشاء لون
/* func Color nR, nG, nB, nA      # nA = 255
    return [nR, nG, nB, nA]
 */
# دالة مساعدة لحساب المسافة بين نقطتين
func Distance aPos1, aPos2
    nDx = aPos1[1] - aPos2[1]
    nDy = aPos1[2] - aPos2[2]
    nDz = aPos1[3] - aPos2[3]
    return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

/* # دالة مساعدة لتطبيع متجه
func Normalize aVector
    nLength = sqrt(aVector[1]*aVector[1] + aVector[2]*aVector[2] + aVector[3]*aVector[3])
    if nLength > 0 {
        return [aVector[1]/nLength, aVector[2]/nLength, aVector[3]/nLength]
    }
    return [0, 0, 0] */

# دالة مساعدة للحصول على اسم الملف من المسار
func ExtractFileName cPath
    nLastSlash = 0
    for i = 1 to len(cPath) {
        if cPath[i] = "/" or cPath[i] = "\" {
            nLastSlash = i
        }
    }
    if nLastSlash > 0 {
        return substr(cPath, nLastSlash + 1)
    }
    return cPath

? "تم تحميل محرك الألعاب Ring Game Engine بنجاح!"
? "الإصدار: 1.0.0"
? "جاهز للاستخدام..."