/*
الكلاس: GameObject
الوصف: كلاس أساسي لجميع كائنات اللعبة في المحرك
المدخلات: اسم الكائن، الموقع، الدوران، الحجم
المخرجات: كائن لعبة قابل للتحكم والتفاعل
*/

//load "raylib.ring"

class GameObject {
    
    func init cName, aPos    # = "GameObject", aPos = [0,0,0]
        cID = generateID()
        cObjectName = cName
        aPosition = aPos
        aRotation = [0, 0, 0]
        aScale = [1, 1, 1]
        bActive = true
        bVisible = true
        aChildren = []
        oParent = null
        aComponents = []
        
        # إعداد المصفوفة التحويلية
        updateTransform()

    func generateID
        # توليد معرف فريد للكائن
        return "GO_" + string(clock()) + "_" + string(random(9999))

    func setPosition aPos
        aPosition = aPos
        updateTransform()
        notifyChildrenTransformChanged()

    func getPosition
        return aPosition

    func setRotation aRot
        aRotation = aRot
        updateTransform()
        notifyChildrenTransformChanged()

    func getRotation
        return aRotation

    func setScale aScl
        aScale = aScl
        updateTransform()
        notifyChildrenTransformChanged()

    func getScale
        return aScale

    func updateTransform
        # حساب المصفوفة التحويلية
        mTransform = MatrixIdentity()
        mTransform = MatrixMultiply(mTransform, MatrixScale(aScale[1], aScale[2], aScale[3]))
        mTransform = MatrixMultiply(mTransform, MatrixRotateXYZ(Vector3(aRotation[1], aRotation[2], aRotation[3])))
        mTransform = MatrixMultiply(mTransform, MatrixTranslate(aPosition[1], aPosition[2], aPosition[3]))

    func getTransform
        return mTransform

    func addChild oChild
        if oChild != null and find(aChildren, oChild) = 0 {
            add(aChildren, oChild)
            oChild.setParent(this)
        }

    func removeChild oChild
        nIndex = find(aChildren, oChild)
        if nIndex > 0 {
            del(aChildren, nIndex)
            oChild.setParent(null)
        }

    func setParent oNewParent
        if oParent != null {
            oParent.removeChild(this)
        }
        oParent = oNewParent

    func getParent
        return oParent

    func getChildren
        return aChildren

    func notifyChildrenTransformChanged
        for oChild in aChildren {
            oChild.onParentTransformChanged()
        }

    func onParentTransformChanged
        updateTransform()
        notifyChildrenTransformChanged()

    func addComponent oComponent
        if oComponent != null {
            add(aComponents, oComponent)
            oComponent.setOwner(this)
            oComponent.onAttach()
        }

    func removeComponent oComponent
        nIndex = find(aComponents, oComponent)
        if nIndex > 0 {
            oComponent.onDetach()
            del(aComponents, nIndex)
        }

    func getComponent cType
        for oComponent in aComponents {
            if oComponent.getType() = cType {
                return oComponent
            }
        }
        return null

    func update nDeltaTime
        if not bActive {
            return
        }
        
        # تحديث المكونات
        for oComponent in aComponents {
            if oComponent.isActive() {
                oComponent.update(nDeltaTime)
            }
        }
        
        # تحديث الأطفال
        for oChild in aChildren {
            oChild.update(nDeltaTime)
        }

    func render
        if not bVisible or not bActive {
            return
        }
        
        # رسم المكونات
        for oComponent in aComponents {
            if oComponent.isVisible() {
                oComponent.render()
            }
        }
        
        # رسم الأطفال
        for oChild in aChildren {
            oChild.render()
        }

    func setActive bState
        bActive = bState
        for oChild in aChildren {
            oChild.setActive(bState)
        }

    func isActive
        return bActive

    func setVisible bState
        bVisible = bState

    func isVisible
        return bVisible

    func getName
        return cObjectName

    func setName cName
        cObjectName = cName

    func getID
        return cID

    func destroy
        # إزالة جميع المكونات
        for oComponent in aComponents {
            oComponent.onDetach()
        }
        aComponents = []
        
        # إزالة جميع الأطفال
        for oChild in aChildren {
            oChild.destroy()
        }
        aChildren = []
        
        # إزالة من الوالد
        if oParent != null {
            oParent.removeChild(this)
        }

    private
        cID = "GameObject"
        cObjectName = "GameObject"
        aPosition = [0,0,0]
        aRotation = [0,0,0]
        aScale = [1,1,1]
        mTransform
        bActive = true
        bVisible = true
        aChildren = []
        oParent = null
        aComponents = []
}
