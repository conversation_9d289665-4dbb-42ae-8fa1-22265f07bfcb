/*
الكلاس: ParticleEmitter
الوصف: مولد الجسيمات مع إعدادات متنوعة
المدخلات: نوع المولد والإعدادات
المخرجات: جسيمات مولدة حسب الإعدادات
*/

//load "Particle.ring"

# أنواع مولدات الجسيمات
EMITTER_TYPE_POINT = 0
EMITTER_TYPE_SPHERE = 1
EMITTER_TYPE_BOX = 2
EMITTER_TYPE_CONE = 3
EMITTER_TYPE_CIRCLE = 4

class ParticleEmitter {
    
    func init cType, aPos # cType = "default", aPos = [0, 0, 0]
        # الموقع والاتجاه
        aPosition = aPos
        aDirection = [0, 1, 0]
        aRotation = [0, 0, 0]
        
        # نوع المولد
        setupEmitterType(cType)
        
        # إعدادات الانبعاث
        nParticlesPerSecond = 100
        nBurstCount = 0
        nBurstInterval = 1.0
        nLastBurstTime = 0.0
        bContinuous = true
        bActive = true
        
        # إعدادات الجسيمات
        nLifetime = 2.0
        nLifetimeVariation = 0.5
        nStartSpeed = 5.0
        nSpeedVariation = 1.0
        nStartSize = 1.0
        nEndSize = 0.1
        nSizeVariation = 0.2
        
        # إعدادات الألوان
        aStartColor = [1, 1, 1, 1]
        aEndColor = [1, 1, 1, 0]
        aColorVariation = [0.1, 0.1, 0.1, 0]
        
        # إعدادات الشكل
        nEmitterSize = 1.0
        nSpread = 45.0  # زاوية الانتشار بالدرجات
        nInnerRadius = 0.0
        nOuterRadius = 1.0
        
        # إعدادات الفيزياء
        bGravityAffected = true
        bWindAffected = true
        nMass = 1.0
        nMassVariation = 0.2
        nDamping = 0.1
        nBounceStrength = 0.5
        
        # إعدادات الدوران
        nRotationSpeed = 0.0
        nRotationVariation = 90.0
        
        # القوام
        oTexture = null
        
        # إحصائيات
        nParticlesEmitted = 0
        nAccumulatedTime = 0.0
        
        # معرف المولد
        cID = generateEmitterID()

    func generateEmitterID
        return "EMITTER_" + string(clock()) + "_" + string(random(99999))

    func setupEmitterType cType
        # إعداد نوع المولد
        switch cType
        on "fire"
            setupFireEmitter()
        on "smoke"
            setupSmokeEmitter()
        on "explosion"
            setupExplosionEmitter()
        on "water"
            setupWaterEmitter()
        on "magic"
            setupMagicEmitter()
        on "sparks"
            setupSparksEmitter()
        other
            setupDefaultEmitter()
        off

    func setupDefaultEmitter
        nEmitterType = EMITTER_TYPE_POINT
        nParticlesPerSecond = 50
        nLifetime = 2.0
        nStartSpeed = 3.0
        nSpread = 30.0
        aStartColor = [1, 1, 1, 1]
        aEndColor = [1, 1, 1, 0]

    func setupFireEmitter
        nEmitterType = EMITTER_TYPE_CONE
        nParticlesPerSecond = 200
        nLifetime = 1.5
        nStartSpeed = 4.0
        nSpread = 25.0
        aStartColor = [1, 0.8, 0.2, 1]
        aEndColor = [1, 0.2, 0, 0]
        nStartSize = 0.5
        nEndSize = 1.5
        bGravityAffected = false

    func setupSmokeEmitter
        nEmitterType = EMITTER_TYPE_SPHERE
        nParticlesPerSecond = 80
        nLifetime = 4.0
        nStartSpeed = 2.0
        nSpread = 60.0
        aStartColor = [0.3, 0.3, 0.3, 0.8]
        aEndColor = [0.6, 0.6, 0.6, 0]
        nStartSize = 0.8
        nEndSize = 3.0
        bGravityAffected = false

    func setupExplosionEmitter
        nEmitterType = EMITTER_TYPE_SPHERE
        nParticlesPerSecond = 0
        nBurstCount = 500
        bContinuous = false
        nLifetime = 2.0
        nStartSpeed = 15.0
        nSpread = 180.0
        aStartColor = [1, 0.5, 0, 1]
        aEndColor = [1, 0, 0, 0]
        nStartSize = 0.3
        nEndSize = 0.1

    func setupWaterEmitter
        nEmitterType = EMITTER_TYPE_CONE
        nParticlesPerSecond = 300
        nLifetime = 3.0
        nStartSpeed = 8.0
        nSpread = 15.0
        aStartColor = [0.2, 0.6, 1, 0.8]
        aEndColor = [0.1, 0.4, 0.8, 0.2]
        nStartSize = 0.2
        nEndSize = 0.1
        bGravityAffected = true
        nBounceStrength = 0.3

    func setupMagicEmitter
        nEmitterType = EMITTER_TYPE_SPHERE
        nParticlesPerSecond = 150
        nLifetime = 2.5
        nStartSpeed = 3.0
        nSpread = 90.0
        aStartColor = [0.8, 0.2, 1, 1]
        aEndColor = [0.4, 0.8, 1, 0]
        nStartSize = 0.4
        nEndSize = 0.8
        bGravityAffected = false
        nRotationSpeed = 180.0

    func setupSparksEmitter
        nEmitterType = EMITTER_TYPE_CONE
        nParticlesPerSecond = 400
        nLifetime = 1.0
        nStartSpeed = 12.0
        nSpread = 45.0
        aStartColor = [1, 1, 0.2, 1]
        aEndColor = [1, 0.3, 0, 0]
        nStartSize = 0.1
        nEndSize = 0.05
        bGravityAffected = true

    func update nDeltaTime
        if not bActive {
            return
        }
        
        nAccumulatedTime += nDeltaTime
        
        # تحديث وقت الانفجار
        if nBurstCount > 0 {
            nLastBurstTime += nDeltaTime
        }

    func getParticlesPerFrame nDeltaTime
        # حساب عدد الجسيمات المطلوب إنشاؤها في هذا الإطار
        nParticlesToEmit = 0
        
        if bContinuous and nParticlesPerSecond > 0 {
            # انبعاث مستمر
            nParticlesToEmit = nParticlesPerSecond * nDeltaTime
        }
        
        if nBurstCount > 0 and nLastBurstTime >= nBurstInterval {
            # انبعاث دفعي
            nParticlesToEmit += nBurstCount
            nLastBurstTime = 0.0
            
            if not bContinuous {
                bActive = false  # إيقاف المولد بعد الانفجار
            }
        }
        
        return floor(nParticlesToEmit)

    func initializeParticle oParticle
        # تهيئة جسيمة جديدة
        if oParticle = null {
            return
        }
        
        # إعادة تعيين الجسيمة
        oParticle.reset()
        
        # تعيين الموقع
        aParticlePos = generateEmissionPosition()
        oParticle.setPosition(aParticlePos)
        
        # تعيين السرعة
        aParticleVel = generateEmissionVelocity()
        oParticle.setVelocity(aParticleVel)
        
        # تعيين الخصائص البصرية
        setupParticleVisuals(oParticle)
        
        # تعيين الخصائص الفيزيائية
        setupParticlePhysics(oParticle)
        
        # تعيين القوام
        oParticle.setTexture(oTexture)
        
        nParticlesEmitted++

    func generateEmissionPosition
        # توليد موقع انبعاث حسب نوع المولد
        switch nEmitterType
        on EMITTER_TYPE_POINT
            return [aPosition[1], aPosition[2], aPosition[3]]
        on EMITTER_TYPE_SPHERE
            return generateSpherePosition()
        on EMITTER_TYPE_BOX
            return generateBoxPosition()
        on EMITTER_TYPE_CONE
            return generateConePosition()
        on EMITTER_TYPE_CIRCLE
            return generateCirclePosition()
        other
            return [aPosition[1], aPosition[2], aPosition[3]]
        off

    func generateSpherePosition
        # توليد موقع عشوائي داخل كرة
        nRadius = random(nOuterRadius - nInnerRadius) + nInnerRadius
        nTheta = random(360) * 3.14159 / 180.0
        nPhi = random(180) * 3.14159 / 180.0
        
        nX = aPosition[1] + nRadius * sin(nPhi) * cos(nTheta)
        nY = aPosition[2] + nRadius * cos(nPhi)
        nZ = aPosition[3] + nRadius * sin(nPhi) * sin(nTheta)
        
        return [nX, nY, nZ]

    func generateBoxPosition
        # توليد موقع عشوائي داخل مكعب
        nHalfSize = nEmitterSize / 2.0
        nX = aPosition[1] + (random(nEmitterSize) - nHalfSize)
        nY = aPosition[2] + (random(nEmitterSize) - nHalfSize)
        nZ = aPosition[3] + (random(nEmitterSize) - nHalfSize)
        
        return [nX, nY, nZ]

    func generateConePosition
        # توليد موقع عشوائي داخل مخروط
        nRadius = random(nEmitterSize)
        nAngle = random(360) * 3.14159 / 180.0
        
        nX = aPosition[1] + nRadius * cos(nAngle)
        nY = aPosition[2]
        nZ = aPosition[3] + nRadius * sin(nAngle)
        
        return [nX, nY, nZ]

    func generateCirclePosition
        # توليد موقع عشوائي على دائرة
        nAngle = random(360) * 3.14159 / 180.0
        nRadius = random(nOuterRadius - nInnerRadius) + nInnerRadius
        
        nX = aPosition[1] + nRadius * cos(nAngle)
        nY = aPosition[2]
        nZ = aPosition[3] + nRadius * sin(nAngle)
        
        return [nX, nY, nZ]

    func generateEmissionVelocity
        # توليد سرعة انبعاث
        nSpeed = nStartSpeed + (random(nSpeedVariation * 2) - nSpeedVariation)
        
        # حساب الاتجاه مع الانتشار
        nSpreadRad = nSpread * 3.14159 / 180.0
        nAngleH = (random(nSpreadRad * 2) - nSpreadRad)
        nAngleV = (random(nSpreadRad * 2) - nSpreadRad)
        
        # تطبيق الدوران على الاتجاه الأساسي
        nDirX = aDirection[1] + sin(nAngleH)
        nDirY = aDirection[2] + sin(nAngleV)
        nDirZ = aDirection[3] + cos(nAngleH) * cos(nAngleV)
        
        # تطبيع الاتجاه
        nLength = sqrt(nDirX*nDirX + nDirY*nDirY + nDirZ*nDirZ)
        if nLength > 0 {
            nDirX /= nLength
            nDirY /= nLength
            nDirZ /= nLength
        }
        
        return [nDirX * nSpeed, nDirY * nSpeed, nDirZ * nSpeed]

    func setupParticleVisuals oParticle
        # إعداد الخصائص البصرية للجسيمة
        
        # العمر
        nParticleLifetime = nLifetime + (random(nLifetimeVariation * 2) - nLifetimeVariation)
        oParticle.setLifetime(nParticleLifetime)
        
        # الحجم
        nParticleStartSize = nStartSize + (random(nSizeVariation * 2) - nSizeVariation)
        oParticle.setStartSize(nParticleStartSize)
        oParticle.setEndSize(nEndSize)
        oParticle.setSize(nParticleStartSize)
        
        # الألوان
        aParticleStartColor = [
            aStartColor[1] + (random(aColorVariation[1] * 2) - aColorVariation[1]),
            aStartColor[2] + (random(aColorVariation[2] * 2) - aColorVariation[2]),
            aStartColor[3] + (random(aColorVariation[3] * 2) - aColorVariation[3]),
            aStartColor[4] + (random(aColorVariation[4] * 2) - aColorVariation[4])
        ]
        
        # تحديد الألوان في النطاق الصحيح
        for i = 1 to 4 {
            if aParticleStartColor[i] < 0 { aParticleStartColor[i] = 0 }
            if aParticleStartColor[i] > 1 { aParticleStartColor[i] = 1 }
        }
        
        oParticle.setStartColor(aParticleStartColor)
        oParticle.setEndColor(aEndColor)
        oParticle.setColor(aParticleStartColor)
        
        # الدوران
        nParticleRotationSpeed = nRotationSpeed + (random(nRotationVariation * 2) - nRotationVariation)
        oParticle.setRotationSpeed(nParticleRotationSpeed * 3.14159 / 180.0)  # تحويل للراديان

    func setupParticlePhysics oParticle
        # إعداد الخصائص الفيزيائية للجسيمة
        
        # الكتلة
        nParticleMass = nMass + (random(nMassVariation * 2) - nMassVariation)
        if nParticleMass <= 0 { nParticleMass = 0.1 }
        oParticle.setMass(nParticleMass)
        
        # الخصائص الأخرى
        oParticle.setDamping(nDamping)
        oParticle.setBounceStrength(nBounceStrength)
        oParticle.setGravityAffected(bGravityAffected)
        oParticle.setWindAffected(bWindAffected)

    func setPosition aPos
        aPosition = aPos

    func getPosition
        return aPosition

    func setDirection aDir
        aDirection = aDir

    func getDirection
        return aDirection

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func setTexture oTex
        oTexture = oTex

    func getTexture
        return oTexture

    func getParticlesEmitted
        return nParticlesEmitted

    func getID
        return cID

    private
        cID
        aPosition
        aDirection
        aRotation
        nEmitterType
        nParticlesPerSecond
        nBurstCount
        nBurstInterval
        nLastBurstTime
        bContinuous
        bActive
        nLifetime
        nLifetimeVariation
        nStartSpeed
        nSpeedVariation
        nStartSize
        nEndSize
        nSizeVariation
        aStartColor
        aEndColor
        aColorVariation
        nEmitterSize
        nSpread
        nInnerRadius
        nOuterRadius
        bGravityAffected
        bWindAffected
        nMass
        nMassVariation
        nDamping
        nBounceStrength
        nRotationSpeed
        nRotationVariation
        oTexture
        nParticlesEmitted
        nAccumulatedTime
}
