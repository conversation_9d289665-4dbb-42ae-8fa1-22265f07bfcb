/*
الكلاس: Mesh<PERSON>enderer
الوصف: مكون لعرض الشبكات ثلاثية الأبعاد
المدخلات: شبكة ومادة للعرض
المخرجات: عرض بصري للكائن
*/

/* load "Component.ring"
load "Mesh.ring"
load "GameMaterial.ring" */

class MeshRenderer from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("MeshRenderer")
        
        # الشبكة والمادة
        oMesh = null
        oMaterial = null
        
        # خصائص العرض
        bCastShadows = true
        bReceiveShadows = true
        bVisible = true
        nRenderLayer = 0
        
        # إحصائيات العرض
        nTrianglesRendered = 0
        nVerticesRendered = 0

    func createCube nSize # = 1.0
        # إنشاء شبكة مكعب
        oMesh = new GameMesh("Cube")
        
        # إنشاء رؤوس المكعب
        nHalf = nSize / 2.0
        aVertices = [
            # الوجه الأمامي
            [-nHalf, -nHalf,  nHalf],  # 0
            [ nHalf, -nHalf,  nHalf],  # 1
            [ nHalf,  nHalf,  nHalf],  # 2
            [-nHalf,  nHalf,  nHalf],  # 3
            
            # الوجه الخلفي
            [-nHalf, -nHalf, -nHalf],  # 4
            [ nHalf, -nHalf, -nHalf],  # 5
            [ nHalf,  nHalf, -nHalf],  # 6
            [-nHalf,  nHalf, -nHalf]   # 7
        ]
        
        # فهارس المثلثات
        aIndices = [
            # الوجه الأمامي
            0, 1, 2,  2, 3, 0,
            # الوجه الخلفي
            4, 6, 5,  6, 4, 7,
            # الوجه الأيسر
            4, 0, 3,  3, 7, 4,
            # الوجه الأيمن
            1, 5, 6,  6, 2, 1,
            # الوجه العلوي
            3, 2, 6,  6, 7, 3,
            # الوجه السفلي
            4, 5, 1,  1, 0, 4
        ]
        
        oMesh.setVertices(aVertices)
        oMesh.setIndices(aIndices)
        oMesh.calculateNormals()
        oMesh.calculateBounds()

    func createSphere nRadius, nSegments    # nRadius = 1.0, nSegments = 16
        # إنشاء شبكة كرة
        oMesh = new GameMesh("Sphere")
        
        aVertices = []
        aIndices = []
        
        # إنشاء رؤوس الكرة
        for nLat = 0 to nSegments {
            nTheta = (nLat * 3.14159) / nSegments
            nSinTheta = sin(nTheta)
            nCosTheta = cos(nTheta)
            
            for nLon = 0 to nSegments {
                nPhi = (nLon * 2 * 3.14159) / nSegments
                nSinPhi = sin(nPhi)
                nCosPhi = cos(nPhi)
                
                nX = nRadius * nSinTheta * nCosPhi
                nY = nRadius * nCosTheta
                nZ = nRadius * nSinTheta * nSinPhi
                
                add(aVertices, [nX, nY, nZ])
            }
        }
        
        # إنشاء فهارس المثلثات
        for nLat = 0 to nSegments - 1 {
            for nLon = 0 to nSegments - 1 {
                nFirst = (nLat * (nSegments + 1)) + nLon
                nSecond = nFirst + nSegments + 1
                
                # المثلث الأول
                add(aIndices, nFirst)
                add(aIndices, nSecond)
                add(aIndices, nFirst + 1)
                
                # المثلث الثاني
                add(aIndices, nSecond)
                add(aIndices, nSecond + 1)
                add(aIndices, nFirst + 1)
            }
        }
        
        oMesh.setVertices(aVertices)
        oMesh.setIndices(aIndices)
        oMesh.calculateNormals()
        oMesh.calculateBounds()

    func createPlane nWidth, nHeight    # nWidth = 10.0, nHeight = 10.0
        # إنشاء شبكة مستوى
        oMesh = new GameMesh("Plane")
        
        nHalfW = nWidth / 2.0
        nHalfH = nHeight / 2.0
        
        aVertices = [
            [-nHalfW, 0, -nHalfH],  # 0
            [ nHalfW, 0, -nHalfH],  # 1
            [ nHalfW, 0,  nHalfH],  # 2
            [-nHalfW, 0,  nHalfH]   # 3
        ]
        
        aIndices = [
            0, 1, 2,  2, 3, 0
        ]
        
        oMesh.setVertices(aVertices)
        oMesh.setIndices(aIndices)
        oMesh.calculateNormals()
        oMesh.calculateBounds()

    func loadModel cPath
        # تحميل نموذج من ملف
        try {
            oMesh = new GameMesh("LoadedModel")
            # هنا يجب تحميل النموذج من الملف
            # هذا يتطلب دعم من RayLib أو مكتبة أخرى
            ? "تحميل النموذج: " + cPath
            return true
        catch
            ? "خطأ في تحميل النموذج: " + cPath
            return false
        }

    func setMaterial oNewMaterial
        oMaterial = oNewMaterial

    func getMaterial
        return oMaterial

    func setVisible bState
        bVisible = bState

    func isVisible
        return bVisible

    func setCastShadows bState
        bCastShadows = bState

    func getCastShadows
        return bCastShadows

    func setReceiveShadows bState
        bReceiveShadows = bState

    func getReceiveShadows
        return bReceiveShadows

    func setRenderLayer nLayer
        nRenderLayer = nLayer

    func getRenderLayer
        return nRenderLayer

    func render mTransform
        # رسم الشبكة
        if not bVisible or oMesh = null {
            return
        }
        
        # تطبيق المادة
        if oMaterial != null {
            oMaterial.apply()
        }
        
        # رسم الشبكة
        oMesh.draw(mTransform)
        
        # تحديث الإحصائيات
        nTrianglesRendered = oMesh.getTriangleCount()
        nVerticesRendered = oMesh.getVertexCount()

    func update nDeltaTime
        # تحديث المكون
        # لا يحتاج MeshRenderer لتحديث عادة

    func getMesh
        return oMesh

    func getTrianglesRendered
        return nTrianglesRendered

    func getVerticesRendered
        return nVerticesRendered
        
    func setGameMaterial oGameMaterial
        oGameMaterial = oGameMaterial

    func getGameMaterial
        return oGameMaterial

    private
        oMesh
        oMaterial
        bCastShadows
        bReceiveShadows
        bVisible
        nRenderLayer
        nTrianglesRendered
        nVerticesRendered
}
