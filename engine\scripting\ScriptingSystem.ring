//load "stdlib.ring"

class ScriptingSystem {
    

    func init
        oScriptEngine = new ScriptEngine
        oGlobalContext = new ScriptContext
        initializeAPI()

    func initializeAPI
        # تسجيل الدوال الأساسية للمحرك
        oGlobalContext.registerFunction("move", method(x, y, z) {
            return [x, y, z]
        })

        oGlobalContext.registerFunction("rotate", method(angle) {
            return angle
        })

        oGlobalContext.registerFunction("playAnimation", method(name) {
            return "Playing " + name
        })

        oGlobalContext.registerFunction("spawnObject", method(cType, pos) {
            return new GameObject(cType, pos)
        })

    func loadScript cPath
        cContent = read(cPath)
        oScript = new Script(cContent)
        oScript.compile()
        add(aScripts, oScript)
        return oScript

    func executeScript oScript, aParams
        try {
            oContext = new ScriptContext
            oContext.setParameters(aParams)
            return oScript.execute(oContext)
        catch
            log("Script execution error: " + cCatchError)
            return null
        }

    func attachScriptToObject oScript, oGameObject
        oGameObject.attachScript(oScript)
        oScript.setOwner(oGameObject)

    private
        aScripts = []
        oScriptEngine
        oGlobalContext
}

class Script {
    
    func init cSrc
        cSource = cSrc

    func compile
        try {
            oCompiledCode = new ByteCode(cSource)
            return true
        catch 
            log("Compilation error: " + cCatchError)
            return false
        }

    func execute oContext
        if not oCompiledCode {
            return null
        }
        
        try {
            return oCompiledCode.run(oContext)
        catch
            log("Runtime error: " + cCatchError)
            return null
        }

    func setOwner oObj
        oOwner = oObj

    func getOwner
        return oOwner

    private
        cSource
        oCompiledCode
        oOwner
}

class ScriptContext {
    

    func setVariable cName, xValue
        aVariables[cName] = xValue

    func getVariable cName
        return aVariables[cName]

    func registerFunction cName, fpFunction
        aFunctions[cName] = fpFunction

    func callFunction cName, aArgs
        if find(aFunctions, cName) {
            cFunction = aFunctions[cName]
            return call cFunction (aArgs)
        }
        return null

    func setParameters aParams
        for cKey in aParams {
            for xValue in aParams {
                setVariable(cKey, xValue)
            }
        }
    private
        aVariables = []
        aFunctions = []
}
