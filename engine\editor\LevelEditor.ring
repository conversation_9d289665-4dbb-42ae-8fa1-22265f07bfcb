//load "raylib.ring"

class LevelEditor {
    
    func init
        oGrid = new EditorGrid(50, 50, nGridSize)
        oObjectPalette = new ObjectPalette([
            "Player": "models/player.obj",
            "Enemy": "models/enemy.obj",
            "Tree": "models/tree.obj",
            "Rock": "models/rock.obj",
            "House": "models/house.obj"
        ])
        oPropertyEditor = new PropertyEditor
        oTerrainEditor = new TerrainEditor
        oLightingEditor = new LightingEditor
        initializeTools()

    func initializeTools
        aTools = [
            new SelectTool(),
            new MoveTool(),
            new RotateTool(),
            new ScaleTool(),
            new TerrainTool(),
            new PaintTool()
        ]
        setCurrentTool(aTools[1])  # Select tool by default

    func update
        handleInput()
        updateCurrentTool()
        updateEditors()
        render()

    func handleInput
        if IsMouseButtonPressed(MOUSE_LEFT_BUTTON) {
            handleSelection()
        }

        if IsKeyPressed(KEY_G) {
            bSnapToGrid = !bSnapToGrid
        }

        if IsKeyPressed(KEY_S) and IsKeyDown(KEY_LEFT_CONTROL) {
            saveLevel()
        }

    func handleSelection
        oRay = GetMouseRay(GetMousePosition(), GetCamera())
        oHit = performRaycast(oRay)
        
        if oHit {
            selectObject(oHit.object)
        else
            clearSelection()
        }

    func selectObject oObject
        oSelection = oObject
        oPropertyEditor.setTarget(oObject)
        updateGizmos()

    func clearSelection
        oSelection = null
        oPropertyEditor.clearTarget()
        hideGizmos()

    func placeObject cType, aPosition
        oObject = oObjectPalette.createObject(cType)
        if bSnapToGrid {
            aPosition = oGrid.snapPosition(aPosition)
        }
        oObject.setPosition(aPosition)
        getCurrentScene().addObject(oObject)
        return oObject

    func deleteSelectedObject
        if oSelection {
            getCurrentScene().removeObject(oSelection)
            clearSelection()
        }

    func updateCurrentTool
        if oCurrentTool {
            oCurrentTool.update()
        }

    func updateEditors
        oPropertyEditor.update()
        oTerrainEditor.update()
        oLightingEditor.update()

    func render
        BeginDrawing()
            ClearBackground(RAYWHITE)
            BeginMode3D(GetCamera())
                # رسم الشبكة
                if bSnapToGrid {
                    oGrid.draw()
                }
                
                # رسم الكائنات
                getCurrentScene().render()
                
                # رسم أدوات التحرير
                if oCurrentTool {
                    oCurrentTool.draw()
                }
                
                # رسم التحديد
                if oSelection {
                    drawSelectionHighlight()
                }
            EndMode3D()
            
            # رسم واجهة المستخدم
            drawUI()
        EndDrawing()

    func saveLevel
        cPath = "levels/level1.json"
        oData = [
            :objects = getCurrentScene().serializeObjects(),
            :terrain = oTerrainEditor.serializeTerrain(),
            :lighting = oLightingEditor.serializeLighting()
        ]
        write(cPath, JSON.encode(oData))
        log("Level saved to: " + cPath)

    func loadLevel cPath
        try {
            cContent = read(cPath)
            oData = JSON.decode(cContent)
            
            getCurrentScene().clear()
            getCurrentScene().deserializeObjects(oData["objects"])
            oTerrainEditor.deserializeTerrain(oData["terrain"])
            oLightingEditor.deserializeLighting(oData["lighting"])
            
            log("Level loaded from: " + cPath)
        catch
            log("Error loading level: " + cCatchError)
        }

    private
        oGrid
        oObjectPalette
        oPropertyEditor
        oTerrainEditor
        oLightingEditor
        oCurrentTool
        oSelection
        bSnapToGrid = true
        nGridSize = 1


    func drawSelectionHighlight
        if oSelection {
            oBounds = oSelection.getBoundingBox()
            DrawBoundingBox(oBounds, YELLOW)
        }

    func drawUI
        # رسم شريط الأدوات
        DrawRectangle(10, 10, 200, 30, LIGHTGRAY)
        DrawText("Tools:", 15, 15, 20, BLACK)
        
        # رسم لوحة الكائنات
        oObjectPalette.draw(10, 50)
        
        # رسم محرر الخصائص
        oPropertyEditor.draw(GetScreenWidth() - 210, 10)
        
        # رسم معلومات الشبكة
        if bSnapToGrid {
            DrawText("Grid: " + nGridSize, 10, 
                    GetScreenHeight() - 30, 20, DARKGRAY)
        }
}
