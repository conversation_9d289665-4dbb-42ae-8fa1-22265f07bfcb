/*
الكلاس: AISystem
الوصف: نظام ذكاء اصطناعي متقدم مع دعم السلوك والتنقل والإدراك
المدخلات: كائنات الذكاء الاصطناعي والبيئة
المخرجات: سلوك ذكي وتفاعلي للكائنات
*/

//load "raylib.ring"

class AISystem {

    func init
        # أنظمة التنقل
        oNavMesh = new NavigationMesh()
        oSpatialGrid = new SpatialGrid(100, 100, 5)
        aPathfinders = []

        # أنظمة السلوك
        aBehaviorTrees = []
        aStateManagers = []
        aAIAgents = []

        # أنظمة الإدراك
        aPerceptionSources = []
        aVisionSystems = []
        aHearingSystems = []

        # أنظمة التعلم
        aLearningAgents = []
        aDecisionTrees = []

        # أنظمة السلوك الجماعي
        aFlocks = []
        aSwarms = []

        # إعدادات النظام
        bEnabled = true
        nUpdateFrequency = 60  # تحديثات في الثانية
        nLastUpdateTime = 0

        # إحصائيات النظام
        nActiveAgents = 0
        nActiveBehaviorTrees = 0
        nActivePathfinders = 0

        initializeAISystem()

    func initializeAISystem
        # تهيئة النظام
        ? "تهيئة نظام الذكاء الاصطناعي..."

        # إعداد الشبكة المكانية
        oSpatialGrid.initialize()

        # إعداد شبكة التنقل
        oNavMesh.initialize()

        ? "تم تهيئة نظام الذكاء الاصطناعي"

    # إدارة الوكلاء الذكيين
    func createAIAgent cType, oGameObject  # cType = "basic", oGameObject = null
        oAgent = new AIAgent(cType, oGameObject)
        add(aAIAgents, oAgent)
        nActiveAgents++
        return oAgent

    func removeAIAgent oAgent
        nIndex = find(aAIAgents, oAgent)
        if nIndex > 0 {
            del(aAIAgents, nIndex)
            nActiveAgents--
            return true
        }
        return false

    # إدارة أشجار السلوك
    func createBehaviorTree cName    # = "BehaviorTree"
        oBT = new BehaviorTree(cName)
        add(aBehaviorTrees, oBT)
        nActiveBehaviorTrees++
        return oBT

    func removeBehaviorTree oBT
        nIndex = find(aBehaviorTrees, oBT)
        if nIndex > 0 {
            del(aBehaviorTrees, nIndex)
            nActiveBehaviorTrees--
            return true
        }
        return false

    # إدارة آلات الحالة
    func createStateMachine cName    # = "StateMachine"
        oFSM = new FiniteStateMachine(cName)
        add(aStateManagers, oFSM)
        return oFSM

    func removeStateMachine oFSM
        nIndex = find(aStateManagers, oFSM)
        if nIndex > 0 {
            del(aStateManagers, nIndex)
            return true
        }
        return false

    # إدارة التنقل والمسارات
    func createPathfinder cType    # = "AStar"
        oPathfinder = new Pathfinder(cType, oNavMesh)
        add(aPathfinders, oPathfinder)
        nActivePathfinders++
        return oPathfinder

    func findPath aStart, aEnd, cAlgorithm   #= "AStar"
        # البحث عن مسار باستخدام خوارزمية محددة
        switch cAlgorithm
        on "AStar"
            return oNavMesh.findPathAStar(aStart, aEnd)
        on "Dijkstra"
            return oNavMesh.findPathDijkstra(aStart, aEnd)
        on "JPS"  # Jump Point Search
            return oNavMesh.findPathJPS(aStart, aEnd)
        other
            return oNavMesh.findPathAStar(aStart, aEnd)
        off

    func findPathWithConstraints aStart, aEnd, aConstraints
        # البحث عن مسار مع قيود
        return oNavMesh.findPathWithConstraints(aStart, aEnd, aConstraints)

    # إدارة الإدراك والحس
    func addPerceptionSource oSource, nRange, cType    # = "vision"
        oPerceptionData = [
            :source = oSource,
            :range = nRange,
            :type = cType,
            :active = true
        ]
        add(aPerceptionSources, oPerceptionData)
        oSpatialGrid.addObject(oSource, nRange)

    func removePerceptionSource oSource
        for i = len(aPerceptionSources) to 1 step -1 {
            if aPerceptionSources[i][:source] = oSource {
                del(aPerceptionSources, i)
                oSpatialGrid.removeObject(oSource)
                return true
            }
        }
        return false

    func getPerceptibleObjects oObserver, nRange, cType    # = "all"
        # الحصول على الكائنات المدركة
        aObjects = oSpatialGrid.queryRange(oObserver.getPosition(), nRange)

        if cType = "all" {
            return aObjects
        }

        # تصفية حسب النوع
        aFilteredObjects = []
        for oObject in aObjects {
            if oObject.getType() = cType {
                add(aFilteredObjects, oObject)
            }
        }
        return aFilteredObjects

    func checkLineOfSight oObserver, oTarget
        # فحص خط الرؤية بين كائنين
        aObserverPos = oObserver.getPosition()
        aTargetPos = oTarget.getPosition()

        # فحص مبسط للعوائق
        return oNavMesh.hasLineOfSight(aObserverPos, aTargetPos)

    # السلوك الجماعي
    func createFlockingBehavior aAgents, aSettings    # = []
        oFlock = new FlockingBehavior(aAgents, aSettings)
        add(aFlocks, oFlock)
        return oFlock

    func createSwarmBehavior aAgents, aSettings    # = []
        oSwarm = new SwarmBehavior(aAgents, aSettings)
        add(aSwarms, oSwarm)
        return oSwarm

    # التعلم والتكيف
    func createReinforcementLearner cType, aSettings    # cType = "QLearning", aSettings = []
        switch cType
        on "QLearning"
            oLearner = new QLearning(aSettings)
        on "SARSA"
            oLearner = new SARSA(aSettings)
        on "DeepQ"
            oLearner = new DeepQLearning(aSettings)
        other
            oLearner = new QLearning(aSettings)
        off

        add(aLearningAgents, oLearner)
        return oLearner

    func createDecisionTree aTrainingData
        oTree = new DecisionTree()
        oTree.train(aTrainingData)
        add(aDecisionTrees, oTree)
        return oTree

    # التحديث والمعالجة
    func update nDeltaTime
        if not bEnabled {
            return
        }

        # فحص تكرار التحديث
        nCurrentTime = GetTime()
        if nCurrentTime - nLastUpdateTime < (1.0 / nUpdateFrequency) {
            return
        }
        nLastUpdateTime = nCurrentTime

        # تحديث الأنظمة المختلفة
        updateAIAgents(nDeltaTime)
        updateBehaviorTrees(nDeltaTime)
        updateStateMachines(nDeltaTime)
        updatePathfinders(nDeltaTime)
        updatePerceptionSystems(nDeltaTime)
        updateFlockingBehaviors(nDeltaTime)
        updateLearningAgents(nDeltaTime)
        updateSpatialGrid(nDeltaTime)

    func updateAIAgents nDeltaTime
        for oAgent in aAIAgents {
            if oAgent.isActive() {
                oAgent.update(nDeltaTime)
            }
        }

    func updateBehaviorTrees nDeltaTime
        for oBT in aBehaviorTrees {
            if oBT.isActive() {
                oBT.update(nDeltaTime)
            }
        }

    func updateStateMachines nDeltaTime
        for oFSM in aStateManagers {
            if oFSM.isActive() {
                oFSM.update(nDeltaTime)
            }
        }

    func updatePathfinders nDeltaTime
        for oPathfinder in aPathfinders {
            if oPathfinder.isActive() {
                oPathfinder.update(nDeltaTime)
            }
        }

    func updatePerceptionSystems nDeltaTime
        # تحديث أنظمة الإدراك
        for oPerceptionData in aPerceptionSources {
            if oPerceptionData[:active] {
                updatePerceptionSource(oPerceptionData, nDeltaTime)
            }
        }

    func updatePerceptionSource oPerceptionData, nDeltaTime
        # تحديث مصدر إدراك واحد
        oSource = oPerceptionData[:source]
        nRange = oPerceptionData[:range]
        cType = oPerceptionData[:type]

        # تحديث موقع المصدر في الشبكة المكانية
        oSpatialGrid.updateObject(oSource)

    func updateFlockingBehaviors nDeltaTime
        for oFlock in aFlocks {
            if oFlock.isActive() {
                oFlock.update(nDeltaTime)
            }
        }

        for oSwarm in aSwarms {
            if oSwarm.isActive() {
                oSwarm.update(nDeltaTime)
            }
        }

    func updateLearningAgents nDeltaTime
        for oLearner in aLearningAgents {
            if oLearner.isActive() {
                oLearner.update(nDeltaTime)
            }
        }

    func updateSpatialGrid nDeltaTime
        oSpatialGrid.update(nDeltaTime)

    # إدارة النظام
    func setEnabled bState
        bEnabled = bState

    func isEnabled
        return bEnabled

    func setUpdateFrequency nFreq
        nUpdateFrequency = nFreq

    func getUpdateFrequency
        return nUpdateFrequency

    func getActiveAgents
        return nActiveAgents

    func getActiveBehaviorTrees
        return nActiveBehaviorTrees

    func getActivePathfinders
        return nActivePathfinders

    func getStatistics
        return [
            :activeAgents = nActiveAgents,
            :activeBehaviorTrees = nActiveBehaviorTrees,
            :activePathfinders = nActivePathfinders,
            :perceptionSources = len(aPerceptionSources),
            :flocks = len(aFlocks),
            :swarms = len(aSwarms),
            :learningAgents = len(aLearningAgents)
        ]

    func cleanup
        # تنظيف موارد نظام الذكاء الاصطناعي
        ? "تنظيف نظام الذكاء الاصطناعي..."

        # تنظيف الوكلاء
        for oAgent in aAIAgents {
            oAgent.cleanup()
        }
        aAIAgents = []

        # تنظيف أشجار السلوك
        for oBT in aBehaviorTrees {
            oBT.cleanup()
        }
        aBehaviorTrees = []

        # تنظيف آلات الحالة
        for oFSM in aStateManagers {
            oFSM.cleanup()
        }
        aStateManagers = []

        # تنظيف أدوات البحث عن المسار
        for oPathfinder in aPathfinders {
            oPathfinder.cleanup()
        }
        aPathfinders = []

        # تنظيف السلوك الجماعي
        for oFlock in aFlocks {
            oFlock.cleanup()
        }
        aFlocks = []

        for oSwarm in aSwarms {
            oSwarm.cleanup()
        }
        aSwarms = []

        # تنظيف وكلاء التعلم
        for oLearner in aLearningAgents {
            oLearner.cleanup()
        }
        aLearningAgents = []

        # تنظيف الأنظمة الأساسية
        oNavMesh.cleanup()
        oSpatialGrid.cleanup()

        ? "تم تنظيف نظام الذكاء الاصطناعي"

    private
        # أنظمة التنقل
        oNavMesh
        oSpatialGrid
        aPathfinders

        # أنظمة السلوك
        aBehaviorTrees
        aStateManagers
        aAIAgents

        # أنظمة الإدراك
        aPerceptionSources
        aVisionSystems
        aHearingSystems

        # أنظمة التعلم
        aLearningAgents
        aDecisionTrees

        # أنظمة السلوك الجماعي
        aFlocks
        aSwarms

        # إعدادات النظام
        bEnabled
        nUpdateFrequency
        nLastUpdateTime

        # إحصائيات النظام
        nActiveAgents
        nActiveBehaviorTrees
        nActivePathfinders
}

# فئات مساعدة للذكاء الاصطناعي

# كلاس الوكيل الذكي
/*class AIAgent {

    func init cAgentType, oGameObj    # cAgentType = "basic", oGameObj = null
        cType = cAgentType
        oGameObject = oGameObj
        cID = generateAgentID()

        # السلوك
        oBehaviorTree = null
        oStateMachine = null
        oCurrentGoal = null
        aGoalStack = []

        # التنقل
        oPathfinder = null
        aCurrentPath = []
        nCurrentPathIndex = 0
        aTargetPosition = [0, 0, 0]
        nMovementSpeed = 5.0

        # الإدراك
        nVisionRange = 10.0
        nHearingRange = 15.0
        aPerceivedObjects = []
        aMemory = []

        # الحالة
        bActive = true
        bCanMove = true
        bCanSee = true
        bCanHear = true

        setupAgentType()

    func generateAgentID
        return "AGENT_" + clock() + "_" + random(99999)

    func setupAgentType
        # إعداد نوع الوكيل
        switch cType
        on "guard"
            setupGuardAgent()
        on "patrol"
            setupPatrolAgent()
        on "hunter"
            setupHunterAgent()
        on "civilian"
            setupCivilianAgent()
        other
            setupBasicAgent()
        off

    func setupBasicAgent
        nMovementSpeed = 3.0
        nVisionRange = 8.0
        nHearingRange = 10.0

    func setupGuardAgent
        nMovementSpeed = 2.0
        nVisionRange = 15.0
        nHearingRange = 20.0

    func setupPatrolAgent
        nMovementSpeed = 4.0
        nVisionRange = 12.0
        nHearingRange = 15.0

    func setupHunterAgent
        nMovementSpeed = 6.0
        nVisionRange = 20.0
        nHearingRange = 25.0

    func setupCivilianAgent
        nMovementSpeed = 3.0
        nVisionRange = 6.0
        nHearingRange = 8.0

    func update nDeltaTime
        if not bActive {
            return
        }

        # تحديث الإدراك
        updatePerception()

        # تحديث السلوك
        updateBehavior(nDeltaTime)

        # تحديث التنقل
        updateMovement(nDeltaTime)

    func updatePerception
        # تحديث الإدراك والذاكرة
        if bCanSee {
            updateVision()
        }
        if bCanHear {
            updateHearing()
        }
        updateMemory()

    func updateVision
        # تحديث الرؤية
        # هذا يتطلب تكامل مع نظام الإدراك الرئيسي

    func updateHearing
        # تحديث السمع
        # هذا يتطلب تكامل مع نظام الصوت

    func updateMemory
        # تحديث الذاكرة
        # إزالة الذكريات القديمة
        nCurrentTime = GetTime()
        aNewMemory = []
        for oMemoryItem in aMemory {
            if nCurrentTime - oMemoryItem[:timestamp] < 30.0 {  # 30 ثانية
                add(aNewMemory, oMemoryItem)
            }
        }
        aMemory = aNewMemory

    func updateBehavior nDeltaTime
        # تحديث السلوك
        if oBehaviorTree != null {
            oBehaviorTree.update(nDeltaTime)
        }
        if oStateMachine != null {
            oStateMachine.update(nDeltaTime)
        }

    func updateMovement nDeltaTime
        # تحديث الحركة
        if not bCanMove or len(aCurrentPath) = 0 {
            return
        }

        if oGameObject != null {
            aCurrentPos = oGameObject.getPosition()
            aTargetPos = aCurrentPath[nCurrentPathIndex + 1]

            # حساب الاتجاه
            aDirection = [
                aTargetPos[1] - aCurrentPos[1],
                aTargetPos[2] - aCurrentPos[2],
                aTargetPos[3] - aCurrentPos[3]
            ]

            # تطبيع الاتجاه
            nLength = sqrt(aDirection[1]^2 + aDirection[2]^2 + aDirection[3]^2)
            if nLength > 0 {
                aDirection[1] /= nLength
                aDirection[2] /= nLength
                aDirection[3] /= nLength

                # تحريك الكائن
                aNewPos = [
                    aCurrentPos[1] + aDirection[1] * nMovementSpeed * nDeltaTime,
                    aCurrentPos[2] + aDirection[2] * nMovementSpeed * nDeltaTime,
                    aCurrentPos[3] + aDirection[3] * nMovementSpeed * nDeltaTime
                ]

                oGameObject.setPosition(aNewPos)

                # فحص الوصول للهدف
                nDistance = sqrt((aNewPos[1] - aTargetPos[1])^2 +
                               (aNewPos[2] - aTargetPos[2])^2 +
                               (aNewPos[3] - aTargetPos[3])^2)

                if nDistance < 0.5 {  # وصل للهدف
                    nCurrentPathIndex++
                    if nCurrentPathIndex >= len(aCurrentPath) {
                        # انتهى المسار
                        aCurrentPath = []
                        nCurrentPathIndex = 0
                    }
                }
            }
        }

    func setPath aPath
        aCurrentPath = aPath
        nCurrentPathIndex = 0

    func setGoal oGoal
        oCurrentGoal = oGoal

    func addMemory cType, oData
        oMemoryItem = [
            :type = cType,
            :data = oData,
            :timestamp = GetTime()
        ]
        add(aMemory, oMemoryItem)

    func setBehaviorTree oBT
        oBehaviorTree = oBT

    func setStateMachine oFSM
        oStateMachine = oFSM

    func isActive
        return bActive

    func setActive bState
        bActive = bState

    func getID
        return cID

    func getType
        return cType

    func cleanup
        oBehaviorTree = null
        oStateMachine = null
        aCurrentPath = []
        aMemory = []

    private
        cID
        cType = "basic"
        oGameObject = null
        oBehaviorTree
        oStateMachine
        oCurrentGoal
        aGoalStack
        oPathfinder
        aCurrentPath
        nCurrentPathIndex
        aTargetPosition
        nMovementSpeed
        nVisionRange
        nHearingRange
        aPerceivedObjects
        aMemory
        bActive
        bCanMove
        bCanSee
        bCanHear
}
