# مرجع API - محرك الألعاب Ring Game Engine

## 📖 نظرة عامة

هذا المرجع يحتوي على وثائق شاملة لجميع الكلاسات والدوال في محرك الألعاب.

## 🏗️ الكلاسات الأساسية

### Engine
الكلاس الرئيسي للمحرك.

```ring
class Engine {
    func init(nWidth, nHeight, cTitle)
    func start()
    func stop()
    func update(nDeltaTime)
    func render()
    func cleanup()
    
    # الحصول على الأنظمة
    func getSceneManager()
    func getRenderer()
    func getPhysicsEngine()
    func getAudioEngine()
    func getInputManager()
    func getResourceManager()
    func getParticleSystem()
    func getAISystem()
}
```

**المعاملات:**
- `nWidth`: عرض النافذة
- `nHeight`: ارتفاع النافذة  
- `cTitle`: عنوان النافذة

**مثال:**
```ring
oEngine = new Engine(1280, 720, "لعبتي")
oEngine.start()
```

---

### GameObject
كائن اللعبة الأساسي.

```ring
class GameObject {
    func init(cName)
    
    # إدارة المكونات
    func addComponent(oComponent)
    func removeComponent(cType)
    func getComponent(cType)
    func hasComponent(cType)
    
    # التحويلات
    func setPosition(aPosition)
    func getPosition()
    func setRotation(aRotation)
    func getRotation()
    func setScale(aScale)
    func getScale()
    
    # الحالة
    func setActive(bActive)
    func isActive()
    func setVisible(bVisible)
    func isVisible()
    
    # التسلسل الهرمي
    func setParent(oParent)
    func getParent()
    func addChild(oChild)
    func removeChild(oChild)
    func getChildren()
    
    # أخرى
    func getName()
    func setName(cName)
    func getID()
    func destroy()
}
```

**مثال:**
```ring
oPlayer = new GameObject("Player")
oPlayer.setPosition([0, 1, 0])
oPlayer.setScale([1.5, 1.5, 1.5])
```

---

### Component
الكلاس الأساسي للمكونات.

```ring
class Component {
    func init(cType)
    func onAttach()
    func onDetach()
    func update(nDeltaTime)
    func getOwner()
    func getType()
    func isActive()
    func setActive(bActive)
}
```

---

## 🎨 نظام العرض

### Renderer
نظام العرض الرئيسي.

```ring
class Renderer {
    func beginFrame(oCamera)
    func endFrame()
    func renderMesh(oMesh, oMaterial, mTransform)
    func renderGameObject(oGameObject)
    func renderScene(oScene)
    
    # الإعدادات
    func setClearColor(oColor)
    func setWireframeMode(bEnabled)
    func setShowNormals(bEnabled)
    func setShowBounds(bEnabled)
    func setShowStats(bEnabled)
    
    # الإحصائيات
    func getDrawCalls()
    func getTrianglesRendered()
    func getVerticesRendered()
    func getStatistics()
}
```

---

### GameCamera
نظام الكاميرا.

```ring
class GameCamera {
    func init(aPosition, aTarget, aUp)
    
    # التحكم
    func setControlMode(nMode)
    func getControlMode()
    func setMovementSpeed(nSpeed)
    func setMouseSensitivity(nSensitivity)
    
    # الخصائص
    func setFieldOfView(nFOV)
    func getFieldOfView()
    func setCameraType(nType)
    func getCameraType()
    
    # الحركة
    func setPosition(aPos)
    func getPosition()
    func setTarget(aTarget)
    func getTarget()
    func lookAt(aTarget)
    
    # التحديث
    func update(nDeltaTime, oInputManager)
    func getRayLibCamera()
}
```

**أنماط التحكم:**
- `CAMERA_CONTROL_FREE`: حرة
- `CAMERA_CONTROL_ORBITAL`: مدارية
- `CAMERA_CONTROL_FIRST_PERSON`: شخص أول
- `CAMERA_CONTROL_THIRD_PERSON`: شخص ثالث

---

### Light3D
نظام الإضاءة ثلاثية الأبعاد.

```ring
class Light3D {
    func init(aPosition, oColor, nType)
    
    # الخصائص الأساسية
    func setPosition(aPos)
    func getPosition()
    func setDirection(aDir)
    func getDirection()
    func setColor(oColor)
    func getColor()
    func setIntensity(nIntensity)
    func getIntensity()
    
    # خصائص الضوء النقطي
    func setRange(nRange)
    func getRange()
    func setAttenuation(nAttenuation)
    func getAttenuation()
    
    # خصائص الضوء المخروطي
    func setInnerCone(nAngle)
    func getInnerCone()
    func setOuterCone(nAngle)
    func getOuterCone()
    
    # الظلال
    func setCastShadows(bState)
    func getCastShadows()
    func setShadowBias(nBias)
    func getShadowBias()
    
    # أخرى
    func setEnabled(bState)
    func isEnabled()
    func getLightType()
    func render()
}
```

**أنواع الإضاءة:**
- `LIGHT_DIRECTIONAL`: اتجاهية
- `LIGHT_POINT`: نقطية
- `LIGHT_SPOT`: مخروطية

---

### Material
نظام المواد.

```ring
class Material {
    func init(cName)
    
    # الألوان
    func setAlbedoColor(oColor)
    func getAlbedoColor()
    func setEmissiveColor(oColor)
    func getEmissiveColor()
    func setSpecularColor(oColor)
    func getSpecularColor()
    
    # القوام
    func setAlbedoTexture(oTexture)
    func getAlbedoTexture()
    func setNormalTexture(oTexture)
    func getNormalTexture()
    func setSpecularTexture(oTexture)
    func getSpecularTexture()
    
    # خصائص PBR
    func setRoughness(nValue)
    func getRoughness()
    func setMetallic(nValue)
    func getMetallic()
    func setEmissiveStrength(nValue)
    func getEmissiveStrength()
    
    # الشفافية
    func setAlpha(nValue)
    func getAlpha()
    func setTransparent(bState)
    func isTransparent()
    
    # أخرى
    func apply()
    func getName()
    func getID()
}
```

---

### Mesh
نظام الشبكات.

```ring
class Mesh {
    func init(cName)
    
    # التحميل والإنشاء
    func loadFromFile(cFilePath)
    func createCube(nSize)
    func createSphere(nRadius, nSegments)
    func createPlane(nWidth, nHeight)
    
    # البيانات
    func getVertexCount()
    func getTriangleCount()
    func getBoundingMin()
    func getBoundingMax()
    func getBoundingCenter()
    func getBoundingRadius()
    
    # المادة
    func setMaterial(oMaterial)
    func getMaterial()
    
    # العرض
    func setVisible(bState)
    func isVisible()
    func draw(mTransform)
    
    # أخرى
    func getName()
    func getID()
}
```

---

## ⚡ نظام الفيزياء

### PhysicsEngine
محرك الفيزياء الرئيسي.

```ring
class PhysicsEngine {
    func init()
    func update(nDeltaTime)
    func cleanup()
    
    # الإعدادات العامة
    func setGravity(aGravity)
    func getGravity()
    func setTimeStep(nTimeStep)
    func getTimeStep()
    func setMaxSubSteps(nSteps)
    func getMaxSubSteps()
    
    # إدارة الأجسام
    func addRigidBody(oBody)
    func removeRigidBody(oBody)
    func getRigidBodies()
    
    # كشف التصادمات
    func setCollisionHandler(oHandler)
    func getCollisionHandler()
    func checkCollision(oBody1, oBody2)
    
    # الاستعلامات
    func raycast(aStart, aEnd)
    func sphereCast(aStart, aEnd, nRadius)
    func boxCast(aStart, aEnd, aSize)
    
    # الإحصائيات
    func getActiveBodyCount()
    func getCollisionCount()
    func getStatistics()
}
```

---

### RigidBody
الجسم الصلب.

```ring
class RigidBody from Component {
    func init()
    
    # الخصائص الأساسية
    func setMass(nMass)
    func getMass()
    func setStatic(bStatic)
    func isStatic()
    func setKinematic(bKinematic)
    func isKinematic()
    
    # المواد الفيزيائية
    func setRestitution(nRestitution)
    func getRestitution()
    func setFriction(nFriction)
    func getFriction()
    func setLinearDamping(nDamping)
    func getLinearDamping()
    func setAngularDamping(nDamping)
    func getAngularDamping()
    
    # الحركة
    func setVelocity(aVelocity)
    func getVelocity()
    func setAngularVelocity(aAngularVelocity)
    func getAngularVelocity()
    func addForce(aForce)
    func addTorque(aTorque)
    func addForceAtPosition(aForce, aPosition)
    
    # القيود
    func freezePosition(bX, bY, bZ)
    func freezeRotation(bX, bY, bZ)
    func setGravityScale(nScale)
    func getGravityScale()
    
    # الحالة
    func isAwake()
    func setAwake(bAwake)
    func isSleeping()
}
```

---

### Collider
كاشف التصادم الأساسي.

```ring
class Collider from Component {
    func init(cShape)
    
    # الخصائص
    func setTrigger(bTrigger)
    func isTrigger()
    func setMaterial(oPhysicsMaterial)
    func getMaterial()
    
    # الحدود
    func getBounds()
    func getCenter()
    func getSize()
    
    # التصادمات
    func checkCollision(oOtherCollider)
    func getContactPoints(oOtherCollider)
    
    # أخرى
    func getShape()
    func isEnabled()
    func setEnabled(bEnabled)
}
```

### BoxCollider
كاشف تصادم مكعب.

```ring
class BoxCollider from Collider {
    func init()
    func setSize(aSize)
    func getSize()
    func setCenter(aCenter)
    func getCenter()
}
```

### SphereCollider
كاشف تصادم كرة.

```ring
class SphereCollider from Collider {
    func init()
    func setRadius(nRadius)
    func getRadius()
    func setCenter(aCenter)
    func getCenter()
}
```

---

## 🔊 نظام الصوت

### AudioEngine
محرك الصوت الرئيسي.

```ring
class AudioEngine {
    func init()
    func update(nDeltaTime)
    func cleanup()
    
    # تحميل الموارد
    func loadSound(cFilePath)
    func loadMusic(cFilePath)
    func unloadSound(oSound)
    func unloadMusic(oMusic)
    
    # التشغيل
    func playSound(oSound, nVolume, nPitch)
    func playMusic(oMusic, bLoop)
    func stopSound(oSound)
    func stopMusic()
    func pauseMusic()
    func resumeMusic()
    
    # الإعدادات العامة
    func setMasterVolume(nVolume)
    func getMasterVolume()
    func setMusicVolume(nVolume)
    func getMusicVolume()
    func setSoundVolume(nVolume)
    func getSoundVolume()
    
    # الصوت ثلاثي الأبعاد
    func setListenerPosition(aPosition)
    func setListenerOrientation(aForward, aUp)
    func setDistanceModel(nModel)
    func setDopplerFactor(nFactor)
    
    # الإحصائيات
    func getActiveSources()
    func getMaxSources()
    func getStatistics()
}
```

---

### AudioSource
مصدر الصوت.

```ring
class AudioSource from Component {
    func init()
    
    # الصوت
    func setSound(oSound)
    func getSound()
    func play()
    func stop()
    func pause()
    func resume()
    
    # الخصائص
    func setVolume(nVolume)
    func getVolume()
    func setPitch(nPitch)
    func getPitch()
    func setLoop(bLoop)
    func isLooping()
    
    # الصوت ثلاثي الأبعاد
    func setPosition(aPosition)
    func getPosition()
    func setMinDistance(nDistance)
    func getMinDistance()
    func setMaxDistance(nDistance)
    func getMaxDistance()
    func setRolloffFactor(nFactor)
    func getRolloffFactor()
    
    # الحالة
    func isPlaying()
    func isPaused()
    func isStopped()
    func getPlaybackPosition()
    func setPlaybackPosition(nPosition)
}
```

---

### AudioListener
مستمع الصوت.

```ring
class AudioListener from Component {
    func init()
    
    # الموقع والاتجاه
    func setPosition(aPosition)
    func getPosition()
    func setOrientation(aForward, aUp)
    func getOrientation()
    
    # الخصائص
    func setGain(nGain)
    func getGain()
    func setVelocity(aVelocity)
    func getVelocity()
}
```

---

## 🤖 نظام الذكاء الاصطناعي

### AISystem
نظام الذكاء الاصطناعي الرئيسي.

```ring
class AISystem {
    func init()
    func update(nDeltaTime)
    func cleanup()
    
    # إدارة الوكلاء
    func createAIAgent(cType, oGameObject)
    func removeAIAgent(oAgent)
    func getActiveAgents()
    
    # أشجار السلوك
    func createBehaviorTree(cName)
    func removeBehaviorTree(oBT)
    func getActiveBehaviorTrees()
    
    # آلات الحالة
    func createStateMachine(cName)
    func removeStateMachine(oFSM)
    
    # التنقل
    func findPath(aStart, aEnd, cAlgorithm)
    func findPathWithConstraints(aStart, aEnd, aConstraints)
    
    # الإدراك
    func addPerceptionSource(oSource, nRange, cType)
    func removePerceptionSource(oSource)
    func getPerceptibleObjects(oObserver, nRange, cType)
    func checkLineOfSight(oObserver, oTarget)
    
    # السلوك الجماعي
    func createFlockingBehavior(aAgents, aSettings)
    func createSwarmBehavior(aAgents, aSettings)
    
    # التعلم
    func createReinforcementLearner(cType, aSettings)
    func createDecisionTree(aTrainingData)
    
    # الإعدادات
    func setEnabled(bState)
    func isEnabled()
    func setUpdateFrequency(nFreq)
    func getUpdateFrequency()
    
    # الإحصائيات
    func getStatistics()
}
```

---

### AIAgent
الوكيل الذكي.

```ring
class AIAgent from Component {
    func init(cType)
    
    # السلوك
    func setBehaviorTree(oBT)
    func getBehaviorTree()
    func setStateMachine(oFSM)
    func getStateMachine()
    func setGoal(oGoal)
    func getCurrentGoal()
    
    # التنقل
    func setPath(aPath)
    func getCurrentPath()
    func setMovementSpeed(nSpeed)
    func getMovementSpeed()
    
    # الإدراك
    func setVisionRange(nRange)
    func getVisionRange()
    func setHearingRange(nRange)
    func getHearingRange()
    func getPerceivedObjects()
    func addMemory(cType, oData)
    func getMemory()
    
    # الحالة
    func setActive(bState)
    func isActive()
    func getType()
    func getID()
}
```

---

### BehaviorTree
شجرة السلوك.

```ring
class BehaviorTree {
    func init(cName)
    
    # إنشاء العقد
    func createSequenceNode(cName)
    func createSelectorNode(cName)
    func createParallelNode(cName)
    func createDecoratorNode(cName, cType)
    func createActionNode(cName, fpAction)
    func createConditionNode(cName, fpCondition)
    
    # إدارة الشجرة
    func setRootNode(oNode)
    func getRootNode()
    func update(nDeltaTime)
    func reset()
    
    # الحالة
    func setActive(bState)
    func isActive()
    func setDebugMode(bState)
    
    # الإحصائيات
    func getStatistics()
    func getSuccessRate()
    func getName()
    func getID()
}
```

---

### FiniteStateMachine
آلة الحالة المحدودة.

```ring
class FiniteStateMachine {
    func init(cName)
    
    # إدارة الحالات
    func addState(cName, oState)
    func removeState(cName)
    func setState(cName)
    func setInitialState(cName)
    func hasState(cName)
    func getState(cName)
    
    # التحديث
    func update(nDeltaTime)
    func changeState(oNewState)
    
    # الحالة الحالية
    func getCurrentState()
    func getCurrentStateName()
    func getPreviousState()
    func getPreviousStateName()
    func getCurrentStateTime()
    
    # الإعدادات
    func setActive(bState)
    func isActive()
    func reset()
    
    # الإحصائيات
    func getStatistics()
    func getName()
    func getID()
}
```

---

## ✨ نظام الجسيمات

### ParticleSystem
نظام الجسيمات الرئيسي.

```ring
class ParticleSystem {
    func init()
    func update(nDeltaTime)
    func render(oCamera)
    func cleanup()
    
    # إدارة المولدات
    func createEmitter(cType, aPos)
    func removeEmitter(oEmitter)
    func getActiveEmitters()
    
    # القوى العامة
    func addGlobalForce(aForce)
    func removeGlobalForce(aForce)
    func setWindForce(aWind)
    func getWindForce()
    func setGravityStrength(nStrength)
    func getGravityStrength()
    
    # الإعدادات
    func setEnabled(bState)
    func isEnabled()
    func setMaxParticles(nMax)
    func getMaxParticles()
    func clearAllParticles()
    func clearAllEmitters()
    
    # الإحصائيات
    func getActiveParticles()
    func getParticlesCreated()
    func getParticlesDestroyed()
}
```

---

### ParticleEmitter
مولد الجسيمات.

```ring
class ParticleEmitter {
    func init(cType, aPos)
    
    # الموقع والاتجاه
    func setPosition(aPos)
    func getPosition()
    func setDirection(aDir)
    func getDirection()
    
    # إعدادات الانبعاث
    func setParticlesPerSecond(nRate)
    func getParticlesPerSecond()
    func setBurstCount(nCount)
    func getBurstCount()
    func setBurstInterval(nInterval)
    func getBurstInterval()
    
    # خصائص الجسيمات
    func setLifetime(nLifetime)
    func getLifetime()
    func setStartSpeed(nSpeed)
    func getStartSpeed()
    func setSpread(nSpread)
    func getSpread()
    func setStartSize(nSize)
    func getStartSize()
    func setEndSize(nSize)
    func getEndSize()
    func setStartColor(aColor)
    func getStartColor()
    func setEndColor(aColor)
    func getEndColor()
    
    # الفيزياء
    func setGravityAffected(bState)
    func isGravityAffected()
    func setWindAffected(bState)
    func isWindAffected()
    func setBounceStrength(nStrength)
    func getBounceStrength()
    
    # الحالة
    func setActive(bState)
    func isActive()
    func getParticlesEmitted()
    func getID()
}
```

---

## 🎮 نظام المدخلات

### InputManager
مدير المدخلات.

```ring
class InputManager {
    func init()
    func update(nDeltaTime)
    
    # خرائط المدخلات
    func addInputMapping(cAction, nKey)
    func removeInputMapping(cAction)
    func hasInputMapping(cAction)
    func getInputMapping(cAction)
    
    # لوحة المفاتيح
    func isKeyDown(nKey)
    func isKeyPressed(nKey)
    func isKeyReleased(nKey)
    func isActionDown(cAction)
    func isActionPressed(cAction)
    func isActionReleased(cAction)
    
    # الفأرة
    func isMouseButtonDown(nButton)
    func isMouseButtonPressed(nButton)
    func isMouseButtonReleased(nButton)
    func getMousePosition()
    func getMouseDelta()
    func getMouseWheelMove()
    func setMouseSensitivity(nSensitivity)
    func getMouseSensitivity()
    
    # وحدة التحكم
    func isGamepadConnected(nGamepad)
    func isGamepadButtonDown(nGamepad, nButton)
    func isGamepadButtonPressed(nGamepad, nButton)
    func isGamepadButtonReleased(nGamepad, nButton)
    func getGamepadLeftStick(nGamepad)
    func getGamepadRightStick(nGamepad)
    func getGamepadLeftTrigger(nGamepad)
    func getGamepadRightTrigger(nGamepad)
    
    # الإعدادات
    func setEnabled(bState)
    func isEnabled()
    func getConnectedGamepads()
}
```

---

## 📁 إدارة الموارد

### ResourceManager
مدير الموارد.

```ring
class ResourceManager {
    func init()
    func cleanup()
    
    # تحميل الموارد
    func loadTexture(cFilePath, cName)
    func loadModel(cFilePath, cName)
    func loadSound(cFilePath, cName)
    func loadMusic(cFilePath, cName)
    func loadShader(cVertexPath, cFragmentPath, cName)
    func loadFont(cFilePath, cName)
    
    # الحصول على الموارد
    func getTexture(cName)
    func getModel(cName)
    func getSound(cName)
    func getMusic(cName)
    func getShader(cName)
    func getFont(cName)
    
    # إدارة الذاكرة
    func unloadResource(cName)
    func unloadAllResources()
    func preloadResources(aResourceList)
    func cleanupUnusedResources()
    
    # الإحصائيات
    func getLoadedResourceCount()
    func getMemoryUsage()
    func getResourceList()
    func isResourceLoaded(cName)
}
```

---

## 🎬 إدارة المشاهد

### SceneManager
مدير المشاهد.

```ring
class SceneManager {
    func init()
    func update(nDeltaTime)
    func render()
    func cleanup()
    
    # إدارة المشاهد
    func addScene(oScene)
    func removeScene(oScene)
    func removeSceneByName(cName)
    func getScene(cName)
    func hasScene(cName)
    func getSceneCount()
    
    # المشهد الحالي
    func setCurrentScene(oScene)
    func setCurrentSceneByName(cName)
    func getCurrentScene()
    func getCurrentSceneName()
    
    # الانتقالات
    func transitionToScene(oScene, nDuration)
    func transitionToSceneByName(cName, nDuration)
    func isTransitioning()
    func getTransitionProgress()
    
    # حفظ وتحميل
    func saveScene(oScene, cFilePath)
    func loadScene(cFilePath)
    func saveCurrentScene(cFilePath)
}
```

---

### Scene
المشهد.

```ring
class Scene {
    func init(cName)
    func update(nDeltaTime)
    func render()
    func cleanup()
    
    # إدارة الكائنات
    func addGameObject(oGameObject)
    func removeGameObject(oGameObject)
    func removeGameObjectByName(cName)
    func getGameObject(cName)
    func getGameObjects()
    func getObjectCount()
    
    # الكاميرات
    func addCamera(oCamera)
    func removeCamera(oCamera)
    func setActiveCamera(oCamera)
    func getActiveCamera()
    func getCameras()
    
    # الإضاءة
    func addLight(oLight)
    func removeLight(oLight)
    func getLights()
    func getLightCount()
    
    # البحث
    func findGameObjectByName(cName)
    func findGameObjectsByTag(cTag)
    func findGameObjectsWithComponent(cComponentType)
    
    # الإحصائيات
    func getTriangleCount()
    func getVertexCount()
    func getBounds()
    
    # حفظ وتحميل
    func save(cFilePath)
    func load(cFilePath)
    
    # أخرى
    func getName()
    func setName(cName)
    func getID()
    func isActive()
    func setActive(bState)
}
```

---

## 🔧 الثوابت والتعدادات

### مفاتيح لوحة المفاتيح
```ring
KEY_A = 65
KEY_B = 66
# ... باقي المفاتيح
KEY_SPACE = 32
KEY_ENTER = 257
KEY_ESCAPE = 256
```

### أزرار الفأرة
```ring
MOUSE_LEFT_BUTTON = 0
MOUSE_RIGHT_BUTTON = 1
MOUSE_MIDDLE_BUTTON = 2
```

### أزرار وحدة التحكم
```ring
GAMEPAD_BUTTON_A = 0
GAMEPAD_BUTTON_B = 1
GAMEPAD_BUTTON_X = 2
GAMEPAD_BUTTON_Y = 3
```

### الألوان
```ring
WHITE = Color(255, 255, 255, 255)
BLACK = Color(0, 0, 0, 255)
RED = Color(255, 0, 0, 255)
GREEN = Color(0, 255, 0, 255)
BLUE = Color(0, 0, 255, 255)
```

### حالات العقد
```ring
NODE_SUCCESS = 0
NODE_FAILURE = 1
NODE_RUNNING = 2
```

---

## 📞 الدعم والمساعدة

للحصول على مساعدة إضافية:
- راجع [دليل المطور](developer_guide.md)
- اطلع على [الأمثلة](../examples/)
- زر [منتدى Ring](https://groups.google.com/forum/#!forum/ring-lang)
- أنشئ [GitHub Issue](https://github.com/ring-lang/game-engine/issues)

---

**هذا المرجع في تطوير مستمر. سيتم إضافة المزيد من التفاصيل والأمثلة قريباً!** 📚✨
