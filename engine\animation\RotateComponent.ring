/*
الكلاس: RotateComponent
الوصف: مكون لدوران الكائنات بشكل مستمر
المدخلات: سرعة ومحور الدوران
المخرجات: دوران مستمر للكائن
*/

//load "../core/Component.ring"

class RotateComponent from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("RotateComponent")
        
        # سرعة الدوران (درجة/ثانية)
        nSpeed = 90.0
        
        # محور الدوران (X, Y, Z)
        aAxis = [0, 1, 0]  # الدوران حول المحور Y افتراضياً
        
        # حالة المكون
        bIsActive = true
        bIsPaused = false
        
        # الدوران المتراكم
        nAccumulatedRotation = 0.0
        
        # إعدادات إضافية
        bUseLocalSpace = true  # استخدام الإحداثيات المحلية أم العالمية
        nMaxRotation = 360.0   # أقصى دوران (0 = لا نهاية)
        bReverseDirection = false
        
        # متغيرات التحكم في التوقيت
        nStartDelay = 0.0      # تأخير قبل بدء الدوران
        nCurrentDelay = 0.0
        bHasStarted = false

    func setSpeed nNewSpeed
        nSpeed = nNewSpeed

    func getSpeed
        return nSpeed

    func setAxis aNewAxis
        # تطبيع المحور
        nLength = sqrt(aNewAxis[1]*aNewAxis[1] + aNewAxis[2]*aNewAxis[2] + aNewAxis[3]*aNewAxis[3])
        if nLength > 0 {
            aAxis = [
                aNewAxis[1] / nLength,
                aNewAxis[2] / nLength,
                aNewAxis[3] / nLength
            ]
        else
            aAxis = [0, 1, 0]  # محور افتراضي
        }

    func getAxis
        return aAxis

    func setActive bState
        bIsActive = bState

    func isActive
        return bIsActive

    func pause
        bIsPaused = true

    func resume
        bIsPaused = false

    func isPaused
        return bIsPaused

    func setUseLocalSpace bState
        bUseLocalSpace = bState

    func getUseLocalSpace
        return bUseLocalSpace

    func setMaxRotation nMax
        nMaxRotation = nMax

    func getMaxRotation
        return nMaxRotation

    func setReverseDirection bReverse
        bReverseDirection = bReverse

    func getReverseDirection
        return bReverseDirection

    func setStartDelay nDelay
        nStartDelay = nDelay
        nCurrentDelay = nDelay
        bHasStarted = false

    func getStartDelay
        return nStartDelay

    func reset
        # إعادة تعيين المكون
        nAccumulatedRotation = 0.0
        nCurrentDelay = nStartDelay
        bHasStarted = false
        bIsPaused = false

    func update nDeltaTime
        if not bIsActive or bIsPaused or oGameObject = null {
            return
        }
        
        # التعامل مع تأخير البداية
        if not bHasStarted {
            if nCurrentDelay > 0 {
                nCurrentDelay -= nDeltaTime
                return
            else
                bHasStarted = true
            }
        }
        
        # حساب مقدار الدوران لهذا الإطار
        nRotationAmount = nSpeed * nDeltaTime
        
        # عكس الاتجاه إذا كان مطلوب
        if bReverseDirection {
            nRotationAmount = -nRotationAmount
        }
        
        # فحص الحد الأقصى للدوران
        if nMaxRotation > 0 {
            if abs(nAccumulatedRotation + nRotationAmount) > nMaxRotation {
                # وصل للحد الأقصى
                nRotationAmount = nMaxRotation - abs(nAccumulatedRotation)
                if nAccumulatedRotation < 0 {
                    nRotationAmount = -nRotationAmount
                }
                bIsActive = false  # إيقاف الدوران
            }
        }
        
        # تحديث الدوران المتراكم
        nAccumulatedRotation += nRotationAmount
        
        # تطبيق الدوران على الكائن
        applyRotation(nRotationAmount)

    func applyRotation nRotationAmount
        # تطبيق الدوران على الكائن
        aCurrentRotation = oGameObject.getRotation()
        
        if bUseLocalSpace {
            # دوران في الإحداثيات المحلية
            aNewRotation = [
                aCurrentRotation[1] + aAxis[1] * nRotationAmount,
                aCurrentRotation[2] + aAxis[2] * nRotationAmount,
                aCurrentRotation[3] + aAxis[3] * nRotationAmount
            ]
        else
            # دوران في الإحداثيات العالمية
            # هذا يتطلب حسابات أكثر تعقيداً مع الكواتيرنيونات
            # للآن سنستخدم دوران بسيط
            aNewRotation = [
                aCurrentRotation[1] + aAxis[1] * nRotationAmount,
                aCurrentRotation[2] + aAxis[2] * nRotationAmount,
                aCurrentRotation[3] + aAxis[3] * nRotationAmount
            ]
        }
        
        # تطبيق الدوران الجديد
        oGameObject.setRotation(aNewRotation)

    func getAccumulatedRotation
        return nAccumulatedRotation

    func setAccumulatedRotation nRotation
        nAccumulatedRotation = nRotation

    func hasReachedMaxRotation
        if nMaxRotation <= 0 {
            return false
        }
        return abs(nAccumulatedRotation) >= nMaxRotation

    func getRemainingRotation
        if nMaxRotation <= 0 {
            return -1  # لا نهاية
        }
        return nMaxRotation - abs(nAccumulatedRotation)

    func setRotationAroundPoint aPoint, nRadius
        # دوران حول نقطة معينة
        if oGameObject = null {
            return
        }
        
        # حساب الموقع الجديد بناءً على الدوران
        nAngleRad = nAccumulatedRotation * 3.14159 / 180.0
        
        aNewPosition = [
            aPoint[1] + cos(nAngleRad) * nRadius,
            aPoint[2],
            aPoint[3] + sin(nAngleRad) * nRadius
        ]
        
        oGameObject.setPosition(aNewPosition)

    func oscillate nAmplitude, nFrequency
        # دوران متذبذب (جيبي)
        nTime = GetTime()
        nOscillation = sin(nTime * nFrequency * 2 * 3.14159) * nAmplitude
        
        aCurrentRotation = oGameObject.getRotation()
        aNewRotation = [
            aCurrentRotation[1] + aAxis[1] * nOscillation,
            aCurrentRotation[2] + aAxis[2] * nOscillation,
            aCurrentRotation[3] + aAxis[3] * nOscillation
        ]
        
        oGameObject.setRotation(aNewRotation)

    func rotateTowards aTargetRotation, nRotationSpeed
        # دوران تدريجي نحو دوران مستهدف
        aCurrentRotation = oGameObject.getRotation()
        
        # حساب الفرق في كل محور
        aDifference = [
            aTargetRotation[1] - aCurrentRotation[1],
            aTargetRotation[2] - aCurrentRotation[2],
            aTargetRotation[3] - aCurrentRotation[3]
        ]
        
        # تطبيع الزوايا (التعامل مع الدوران 360 درجة)
        for i = 1 to 3 {
            while aDifference[i] > 180 {
                aDifference[i] -= 360
            }
            while aDifference[i] < -180 {
                aDifference[i] += 360
            }
        }
        
        # حساب مقدار الدوران لهذا الإطار
        nMaxRotationThisFrame = nRotationSpeed * GetFrameTime()
        
        aNewRotation = [0, 0, 0]
        bReachedTarget = true
        
        for i = 1 to 3 {
            if abs(aDifference[i]) > nMaxRotationThisFrame {
                if aDifference[i] > 0 {
                    aNewRotation[i] = aCurrentRotation[i] + nMaxRotationThisFrame
                else
                    aNewRotation[i] = aCurrentRotation[i] - nMaxRotationThisFrame
                }
                bReachedTarget = false
            else
                aNewRotation[i] = aTargetRotation[i]
            }
        }
        
        oGameObject.setRotation(aNewRotation)
        return bReachedTarget

    func lookAt aTargetPosition
        # توجيه الكائن لينظر نحو موقع معين
        if oGameObject = null {
            return
        }
        
        aCurrentPosition = oGameObject.getPosition()
        
        # حساب الاتجاه
        aDirection = [
            aTargetPosition[1] - aCurrentPosition[1],
            aTargetPosition[2] - aCurrentPosition[2],
            aTargetPosition[3] - aCurrentPosition[3]
        ]
        
        # حساب زوايا الدوران
        nYaw = atan2(aDirection[1], aDirection[3]) * 180.0 / 3.14159
        nPitch = atan2(aDirection[2], sqrt(aDirection[1]*aDirection[1] + aDirection[3]*aDirection[3])) * 180.0 / 3.14159
        
        aCurrentRotation = oGameObject.getRotation()
        aNewRotation = [nPitch, nYaw, aCurrentRotation[3]]
        
        oGameObject.setRotation(aNewRotation)

    func smoothLookAt aTargetPosition, nSmoothSpeed
        # نظر تدريجي نحو موقع معين
        if oGameObject = null {
            return
        }
        
        aCurrentPosition = oGameObject.getPosition()
        
        # حساب الاتجاه المستهدف
        aDirection = [
            aTargetPosition[1] - aCurrentPosition[1],
            aTargetPosition[2] - aCurrentPosition[2],
            aTargetPosition[3] - aCurrentPosition[3]
        ]
        
        # حساب الدوران المستهدف
        nTargetYaw = atan2(aDirection[1], aDirection[3]) * 180.0 / 3.14159
        nTargetPitch = atan2(aDirection[2], sqrt(aDirection[1]*aDirection[1] + aDirection[3]*aDirection[3])) * 180.0 / 3.14159
        
        aTargetRotation = [nTargetPitch, nTargetYaw, 0]
        
        # دوران تدريجي نحو الهدف
        return rotateTowards(aTargetRotation, nSmoothSpeed)

    private
        nSpeed
        aAxis
        bIsActive
        bIsPaused
        nAccumulatedRotation
        bUseLocalSpace
        nMaxRotation
        bReverseDirection
        nStartDelay
        nCurrentDelay
        bHasStarted
}
