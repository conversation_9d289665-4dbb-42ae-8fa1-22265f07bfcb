/*
مثال: عرض الذكاء الاصطناعي
الوصف: مثال يوضح إمكانيات نظام الذكاء الاصطناعي
المدخلات: تفاعل المستخدم
المخرجات: سلوك ذكي للكائنات
*/

load "../../game_engine.ring"

# تشغيل العرض
oDemo = new AIDemo()
oDemo.start()

class AIDemo {
    
    func init
        # إنشاء المحرك
        oEngine = new Engine(1280, 720, "عرض الذكاء الاصطناعي - Ring Game Engine")
        
        # متغيرات العرض
        oPlayer = null
        aGuards = []
        aPatrols = []
        aHunters = []
        aCivilians = []
        aWaypoints = []
        
        # إعداد العرض
        setupDemo()

    func setupDemo
        ? "إعداد عرض الذكاء الاصطناعي..."
        
        # إنشاء المشهد
        createScene()
        
        # إنشاء البيئة
        createEnvironment()
        
        # إنشاء اللاعب
        createPlayer()
        
        # إنشاء الوكلاء الذكيين
        createAIAgents()
        
        # إعداد نقاط الطريق
        createWaypoints()
        
        # إعداد الإضاءة
        setupLighting()
        
        # إعداد الكاميرا
        setupCamera()
        
        ? "تم إعداد عرض الذكاء الاصطناعي"

    func createScene
        oMainScene = new Scene("AIDemo")
        oEngine.getSceneManager().setCurrentScene(oMainScene)

    func createEnvironment
        # إنشاء أرضية
        oGround = new GameObject("Ground",[0, -0.5, 0])
        oGround.setScale([30, 1, 30])
        
        oGroundMesh = new MeshRenderer()
        oGroundMesh.createCube(1.0)
        oGroundMesh.setMaterial(createGroundMaterial())
        oGround.addComponent(oGroundMesh)
        
        oEngine.getSceneManager().getCurrentScene().addGameObject(oGround)
        
        # إنشاء عوائق
        createObstacles()

    func createObstacles
        # إنشاء عوائق في البيئة
        aObstaclePositions = [
            [5, 1, 5], [-5, 1, 5], [5, 1, -5], [-5, 1, -5],
            [0, 1, 8], [0, 1, -8], [8, 1, 0], [-8, 1, 0],
            [10, 1, 10], [-10, 1, -10]
        ]
        
        for i = 1 to len(aObstaclePositions) {
            oObstacle = new GameObject("Obstacle_" + string(i))
            oObstacle.setPosition(aObstaclePositions[i])
            oObstacle.setScale([2, 2, 2])
            
            oObstacleMesh = new MeshRenderer()
            oObstacleMesh.createCube(1.0)
            oObstacleMesh.setMaterial(createObstacleMaterial())
            oObstacle.addComponent(oObstacleMesh)
            
            oEngine.getSceneManager().getCurrentScene().addGameObject(oObstacle)
        }

    func createPlayer
        # إنشاء اللاعب
        oPlayer = new GameObject("Player")
        oPlayer.setPosition([0, 1, 0])
        
        # مكون العرض
        oPlayerMesh = new MeshRenderer()
        oPlayerMesh.createCube(1.0)
        oPlayerMesh.setMaterial(createPlayerMaterial())
        oPlayer.addComponent(oPlayerMesh)
        
        # مكون التحكم
        oPlayerController = new PlayerController()
        oPlayerController.setMovementSpeed(6.0)
        oPlayer.addComponent(oPlayerController)
        
        oEngine.getSceneManager().getCurrentScene().addGameObject(oPlayer)

    func createAIAgents
        # إنشاء حراس
        for i = 1 to 3 {
            oGuard = createGuard(i)
            add(aGuards, oGuard)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oGuard)
        }
        
        # إنشاء دوريات
        for i = 1 to 2 {
            oPatrol = createPatrol(i)
            add(aPatrols, oPatrol)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oPatrol)
        }
        
        # إنشاء صيادين
        for i = 1 to 1 {
            oHunter = createHunter(i)
            add(aHunters, oHunter)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oHunter)
        }
        
        # إنشاء مدنيين
        for i = 1 to 4 {
            oCivilian = createCivilian(i)
            add(aCivilians, oCivilian)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oCivilian)
        }

    func createGuard nIndex
        # إنشاء حارس
        oGuard = new GameObject("Guard_" + string(nIndex))
        
        # موقع عشوائي
        aPos = [random(20) - 10, 1, random(20) - 10]
        oGuard.setPosition(aPos)
        
        # مكون العرض
        oGuardMesh = new MeshRenderer()
        oGuardMesh.createCube(0.8)
        oGuardMesh.setMaterial(createGuardMaterial())
        oGuard.addComponent(oGuardMesh)
        
        # مكون الذكاء الاصطناعي
        oAIAgent = new AIAgent("guard")
        oAIAgent.setVisionRange(12.0)
        oAIAgent.setHearingRange(15.0)
        oAIAgent.setMovementSpeed(2.5)
        oGuard.addComponent(oAIAgent)
        
        # إنشاء شجرة سلوك للحارس
        oBehaviorTree = createGuardBehaviorTree(oGuard)
        oAIAgent.setBehaviorTree(oBehaviorTree)
        
        return oGuard

    func createPatrol nIndex
        # إنشاء دورية
        oPatrol = new GameObject("Patrol_" + string(nIndex))
        
        aPos = [random(16) - 8, 1, random(16) - 8]
        oPatrol.setPosition(aPos)
        
        # مكون العرض
        oPatrolMesh = new MeshRenderer()
        oPatrolMesh.createCube(0.9)
        oPatrolMesh.setMaterial(createPatrolMaterial())
        oPatrol.addComponent(oPatrolMesh)
        
        # مكون الذكاء الاصطناعي
        oAIAgent = new AIAgent("patrol")
        oAIAgent.setVisionRange(10.0)
        oAIAgent.setMovementSpeed(3.5)
        oPatrol.addComponent(oAIAgent)
        
        # إنشاء آلة حالة للدورية
        oStateMachine = createPatrolStateMachine(oPatrol)
        oAIAgent.setStateMachine(oStateMachine)
        
        return oPatrol

    func createHunter nIndex
        # إنشاء صياد
        oHunter = new GameObject("Hunter_" + string(nIndex))
        
        aPos = [random(24) - 12, 1, random(24) - 12]
        oHunter.setPosition(aPos)
        
        # مكون العرض
        oHunterMesh = new MeshRenderer()
        oHunterMesh.createCube(1.1)
        oHunterMesh.setMaterial(createHunterMaterial())
        oHunter.addComponent(oHunterMesh)
        
        # مكون الذكاء الاصطناعي
        oAIAgent = new AIAgent("hunter")
        oAIAgent.setVisionRange(18.0)
        oAIAgent.setHearingRange(22.0)
        oAIAgent.setMovementSpeed(5.0)
        oHunter.addComponent(oAIAgent)
        
        # إنشاء شجرة سلوك للصياد
        oBehaviorTree = createHunterBehaviorTree(oHunter)
        oAIAgent.setBehaviorTree(oBehaviorTree)
        
        return oHunter

    func createCivilian nIndex
        # إنشاء مدني
        oCivilian = new GameObject("Civilian_" + string(nIndex))
        
        aPos = [random(18) - 9, 1, random(18) - 9]
        oCivilian.setPosition(aPos)
        
        # مكون العرض
        oCivilianMesh = new MeshRenderer()
        oCivilianMesh.createCube(0.7)
        oCivilianMesh.setMaterial(createCivilianMaterial())
        oCivilian.addComponent(oCivilianMesh)
        
        # مكون الذكاء الاصطناعي
        oAIAgent = new AIAgent("civilian")
        oAIAgent.setVisionRange(6.0)
        oAIAgent.setMovementSpeed(2.0)
        oCivilian.addComponent(oAIAgent)
        
        # سلوك بسيط للمدني
        oStateMachine = createCivilianStateMachine(oCivilian)
        oAIAgent.setStateMachine(oStateMachine)
        
        return oCivilian

    func createGuardBehaviorTree oGuard
        # إنشاء شجرة سلوك للحارس
        oBehaviorTree = new BehaviorTree("GuardBehavior")
        
        # العقدة الجذر - اختيار
        oRootSelector = oBehaviorTree.createSelectorNode("Root")
        
        # فحص وجود تهديد
        oThreatCheck = oBehaviorTree.createConditionNode("CheckThreat", 
            func() { return checkForThreat(oGuard) })
        
        # مطاردة التهديد
        oPursueSequence = oBehaviorTree.createSequenceNode("Pursue")
        oPursueAction = oBehaviorTree.createActionNode("PursueTarget",
            func(nDeltaTime) { return pursueTarget(oGuard, nDeltaTime) })
        
        oPursueSequence.addChild(oThreatCheck)
        oPursueSequence.addChild(oPursueAction)
        
        # دورية عادية
        oPatrolAction = oBehaviorTree.createActionNode("Patrol",
            func(nDeltaTime) { return patrolArea(oGuard, nDeltaTime) })
        
        # بناء الشجرة
        oRootSelector.addChild(oPursueSequence)
        oRootSelector.addChild(oPatrolAction)
        
        oBehaviorTree.setRootNode(oRootSelector)
        return oBehaviorTree

    func createPatrolStateMachine oPatrol
        # إنشاء آلة حالة للدورية
        oStateMachine = new FiniteStateMachine("PatrolFSM")
        
        # حالة الدورية
        oPatrolState = new State("Patrolling")
        oPatrolState.onUpdate = func(nDeltaTime) { 
            moveToNextWaypoint(oPatrol, nDeltaTime) 
        }
        
        # حالة التحقيق
        oInvestigateState = new State("Investigating")
        oInvestigateState.onUpdate = func(nDeltaTime) { 
            investigateArea(oPatrol, nDeltaTime) 
        }
        
        # حالة المطاردة
        oChaseState = new State("Chasing")
        oChaseState.onUpdate = func(nDeltaTime) { 
            chaseTarget(oPatrol, nDeltaTime) 
        }
        
        # إضافة الحالات
        oStateMachine.addState("Patrolling", oPatrolState)
        oStateMachine.addState("Investigating", oInvestigateState)
        oStateMachine.addState("Chasing", oChaseState)
        
        # تعيين الحالة الأولية
        oStateMachine.setInitialState("Patrolling")
        
        return oStateMachine

    func createHunterBehaviorTree oHunter
        # شجرة سلوك متقدمة للصياد
        oBehaviorTree = new BehaviorTree("HunterBehavior")
        
        # العقدة الجذر
        oRootSelector = oBehaviorTree.createSelectorNode("HunterRoot")
        
        # البحث عن الهدف
        oSearchSequence = oBehaviorTree.createSequenceNode("Search")
        oTargetFound = oBehaviorTree.createConditionNode("TargetFound",
            func() { return findTarget(oHunter) })
        oHuntAction = oBehaviorTree.createActionNode("Hunt",
            func(nDeltaTime) { return huntTarget(oHunter, nDeltaTime) })
        
        oSearchSequence.addChild(oTargetFound)
        oSearchSequence.addChild(oHuntAction)
        
        # البحث العشوائي
        oWanderAction = oBehaviorTree.createActionNode("Wander",
            func(nDeltaTime) { return wanderAround(oHunter, nDeltaTime) })
        
        oRootSelector.addChild(oSearchSequence)
        oRootSelector.addChild(oWanderAction)
        
        oBehaviorTree.setRootNode(oRootSelector)
        return oBehaviorTree

    func createCivilianStateMachine oCivilian
        # آلة حالة بسيطة للمدني
        oStateMachine = new FiniteStateMachine("CivilianFSM")
        
        # حالة التجول
        oWanderState = new State("Wandering")
        oWanderState.onUpdate = func(nDeltaTime) { 
            wanderRandomly(oCivilian, nDeltaTime) 
        }
        
        # حالة الهروب
        oFleeState = new State("Fleeing")
        oFleeState.onUpdate = func(nDeltaTime) { 
            fleeFromDanger(oCivilian, nDeltaTime) 
        }
        
        oStateMachine.addState("Wandering", oWanderState)
        oStateMachine.addState("Fleeing", oFleeState)
        oStateMachine.setInitialState("Wandering")
        
        return oStateMachine

    func createWaypoints
        # إنشاء نقاط طريق للدوريات
        aWaypointPositions = [
            [8, 1, 8], [-8, 1, 8], [-8, 1, -8], [8, 1, -8],
            [0, 1, 12], [12, 1, 0], [0, 1, -12], [-12, 1, 0]
        ]
        
        for aPos in aWaypointPositions {
            oWaypoint = new GameObject("Waypoint")
            oWaypoint.setPosition(aPos)
            add(aWaypoints, oWaypoint)
        }

    func setupLighting
        # إضاءة رئيسية
        oMainLight = new Light3D([0, 20, 10], WHITE, LIGHT_DIRECTIONAL)
        oMainLight.setIntensity(1.0)
        oMainLight.setDirection([0, -1, -0.3])
        oEngine.getSceneManager().getCurrentScene().addLight(oMainLight)

    func setupCamera
        oCamera = new GameCamera([0, 15, 20], [0, 0, 0], [0, 1, 0])
        oCamera.setControlMode(CAMERA_CONTROL_FREE)
        oCamera.setMovementSpeed(10.0)
        
        oEngine.getSceneManager().getCurrentScene().addCamera(oCamera)
        oEngine.getSceneManager().getCurrentScene().setActiveCamera(oCamera)

    # دوال السلوك للذكاء الاصطناعي
    func checkForThreat oAgent
        # فحص وجود تهديد (اللاعب)
        if oPlayer = null {
            return false
        }
        
        aAgentPos = oAgent.getPosition()
        aPlayerPos = oPlayer.getPosition()
        nDistance = calculateDistance(aAgentPos, aPlayerPos)
        
        oAIComponent = oAgent.getComponent("AIAgent")
        if oAIComponent != null {
            return nDistance <= oAIComponent.getVisionRange()
        }
        
        return false

    func pursueTarget oAgent, nDeltaTime
        # مطاردة الهدف
        if oPlayer = null {
            return NODE_FAILURE
        }
        
        aAgentPos = oAgent.getPosition()
        aPlayerPos = oPlayer.getPosition()
        
        # حساب الاتجاه
        aDirection = [
            aPlayerPos[1] - aAgentPos[1],
            0,
            aPlayerPos[3] - aAgentPos[3]
        ]
        
        # تطبيع الاتجاه
        nLength = sqrt(aDirection[1]^2 + aDirection[3]^2)
        if nLength > 0 {
            aDirection[1] /= nLength
            aDirection[3] /= nLength
        }
        
        # تحريك الوكيل
        oAIComponent = oAgent.getComponent("AIAgent")
        if oAIComponent != null {
            nSpeed = oAIComponent.getMovementSpeed()
            aNewPos = [
                aAgentPos[1] + aDirection[1] * nSpeed * nDeltaTime,
                aAgentPos[2],
                aAgentPos[3] + aDirection[3] * nSpeed * nDeltaTime
            ]
            oAgent.setPosition(aNewPos)
        }
        
        return NODE_RUNNING

    func patrolArea oAgent, nDeltaTime
        # دورية في المنطقة
        return wanderAround(oAgent, nDeltaTime)

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDz*nDz)

    # دوال إنشاء المواد
    func createGroundMaterial
        oMaterial = new GameMaterial("GroundMaterial")
        oMaterial.setAlbedoColor(Color(80, 120, 80, 255))
        return oMaterial

    func createPlayerMaterial
        oMaterial = new GameMaterial("PlayerMaterial")
        oMaterial.setAlbedoColor(Color(50, 150, 255, 255))
        return oMaterial

    func createGuardMaterial
        oMaterial = new GameMaterial("GuardMaterial")
        oMaterial.setAlbedoColor(Color(255, 100, 100, 255))
        return oMaterial

    func createPatrolMaterial
        oMaterial = new GameMaterial("PatrolMaterial")
        oMaterial.setAlbedoColor(Color(255, 150, 50, 255))
        return oMaterial

    func createHunterMaterial
        oMaterial = new GameMaterial("HunterMaterial")
        oMaterial.setAlbedoColor(Color(150, 50, 150, 255))
        return oMaterial

    func createCivilianMaterial
        oMaterial = new GameMaterial("CivilianMaterial")
        oMaterial.setAlbedoColor(Color(100, 200, 100, 255))
        return oMaterial

    func createObstacleMaterial
        oMaterial = new GameMaterial("ObstacleMaterial")
        oMaterial.setAlbedoColor(Color(120, 120, 120, 255))
        return oMaterial

    func start
        ? "بدء عرض الذكاء الاصطناعي..."
        ? "استخدم WASD لتحريك اللاعب"
        ? "راقب سلوك الوكلاء الذكيين المختلفين"
        
        oEngine.start()

    private
        oEngine
        oPlayer
        aGuards
        aPatrols
        aHunters
        aCivilians
        aWaypoints
}

