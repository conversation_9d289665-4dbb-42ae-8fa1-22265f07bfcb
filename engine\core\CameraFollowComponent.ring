/*
الكلاس: CameraFollowComponent
الوصف: مكون لجعل الكاميرا تتبع كائن معين
المدخلات: الهدف المراد تتبعه وإعدادات التتبع
المخرجات: حركة سلسة للكاميرا خلف الهدف
*/

//load "Component.ring"

class CameraFollowComponent from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("CameraFollowComponent")
        
        # الهدف المراد تتبعه
        oTarget = null
        
        # إزاحة الكاميرا من الهدف
        aOffset = [0, 5, 10]  # خلف وأعلى الهدف
        
        # إعدادات التتبع
        nFollowSpeed = 5.0      # سرعة تتبع الموقع
        nRotationSpeed = 3.0    # سرعة تتبع الدوران
        nLookAheadDistance = 2.0 # المسافة للنظر أمام الهدف
        
        # إعدادات النعومة
        bSmoothFollow = true
        bSmoothRotation = true
        nSmoothTime = 0.3
        
        # حدود الكاميرا
        nMinDistance = 2.0
        nMaxDistance = 20.0
        nMinHeight = 1.0
        nMaxHeight = 15.0
        
        # إعدادات التصادم
        bAvoidObstacles = true
        nCollisionRadius = 0.5
        
        # حالة الكاميرا
        aCurrentVelocity = [0, 0, 0]
        aDesiredPosition = [0, 0, 0]
        aCurrentPosition = [0, 0, 0]
        
        # إعدادات الاهتزاز
        bShakeEnabled = false
        nShakeIntensity = 0.0
        nShakeDuration = 0.0
        nShakeTimer = 0.0
        aShakeOffset = [0, 0, 0]
        
        # نمط التتبع
        cFollowMode = "third_person"  # third_person, first_person, orbital, free

    func setTarget oNewTarget
        oTarget = oNewTarget
        if oTarget != null {
            # تحديث الموقع الأولي
            updateDesiredPosition()
        }

    func getTarget
        return oTarget

    func setOffset aNewOffset
        aOffset = aNewOffset

    func getOffset
        return aOffset

    func setFollowSpeed nSpeed
        nFollowSpeed = nSpeed

    func getFollowSpeed
        return nFollowSpeed

    func setRotationSpeed nSpeed
        nRotationSpeed = nSpeed

    func getRotationSpeed
        return nRotationSpeed

    func setSmoothFollow bSmooth
        bSmoothFollow = bSmooth

    func getSmoothFollow
        return bSmoothFollow

    func setSmoothRotation bSmooth
        bSmoothRotation = bSmooth

    func getSmoothRotation
        return bSmoothRotation

    func setFollowMode cMode
        cFollowMode = cMode
        # تعديل الإعدادات حسب النمط
        switch cMode
        on "first_person"
            aOffset = [0, 0, 0]
            nFollowSpeed = 10.0
        on "third_person"
            aOffset = [0, 5, 10]
            nFollowSpeed = 5.0
        on "orbital"
            nFollowSpeed = 3.0
        on "free"
            nFollowSpeed = 8.0
        off

    func getFollowMode
        return cFollowMode

    func setDistanceLimits nMin, nMax
        nMinDistance = nMin
        nMaxDistance = nMax

    func setHeightLimits nMin, nMax
        nMinHeight = nMin
        nMaxHeight = nMax

    func update nDeltaTime
        if oTarget = null or oGameObject = null {
            return
        }
        
        # تحديث الموقع المرغوب
        updateDesiredPosition()
        
        # تطبيق التتبع حسب النمط
        switch cFollowMode
        on "third_person"
            updateThirdPerson(nDeltaTime)
        on "first_person"
            updateFirstPerson(nDeltaTime)
        on "orbital"
            updateOrbital(nDeltaTime)
        on "free"
            updateFree(nDeltaTime)
        off
        
        # تطبيق الاهتزاز
        if bShakeEnabled {
            updateShake(nDeltaTime)
        }
        
        # تطبيق الموقع النهائي
        applyFinalPosition()

    func updateDesiredPosition
        # حساب الموقع المرغوب للكاميرا
        aTargetPosition = oTarget.getPosition()
        aTargetRotation = oTarget.getRotation()
        
        # تحويل الإزاحة للإحداثيات العالمية
        nYawRad = aTargetRotation[2] * 3.14159 / 180.0
        
        # حساب الإزاحة المدورة
        aRotatedOffset = [
            aOffset[1] * cos(nYawRad) - aOffset[3] * sin(nYawRad),
            aOffset[2],
            aOffset[1] * sin(nYawRad) + aOffset[3] * cos(nYawRad)
        ]
        
        aDesiredPosition = [
            aTargetPosition[1] + aRotatedOffset[1],
            aTargetPosition[2] + aRotatedOffset[2],
            aTargetPosition[3] + aRotatedOffset[3]
        ]
        
        # تطبيق الحدود
        applyLimits()

    func applyLimits
        # تطبيق حدود المسافة والارتفاع
        aTargetPosition = oTarget.getPosition()
        
        # حساب المسافة الحالية
        nCurrentDistance = calculateDistance(aDesiredPosition, aTargetPosition)
        
        # تطبيق حدود المسافة
        if nCurrentDistance < nMinDistance {
            # الكاميرا قريبة جداً
            aDirection = [
                aDesiredPosition[1] - aTargetPosition[1],
                aDesiredPosition[2] - aTargetPosition[2],
                aDesiredPosition[3] - aTargetPosition[3]
            ]
            nLength = sqrt(aDirection[1]*aDirection[1] + aDirection[2]*aDirection[2] + aDirection[3]*aDirection[3])
            if nLength > 0 {
                aDirection[1] = aDirection[1] / nLength
                aDirection[2] = aDirection[2] / nLength
                aDirection[3] = aDirection[3] / nLength
                
                aDesiredPosition = [
                    aTargetPosition[1] + aDirection[1] * nMinDistance,
                    aTargetPosition[2] + aDirection[2] * nMinDistance,
                    aTargetPosition[3] + aDirection[3] * nMinDistance
                ]
            }
        elseif nCurrentDistance > nMaxDistance
            # الكاميرا بعيدة جداً
            aDirection = [
                aDesiredPosition[1] - aTargetPosition[1],
                aDesiredPosition[2] - aTargetPosition[2],
                aDesiredPosition[3] - aTargetPosition[3]
            ]
            nLength = sqrt(aDirection[1]*aDirection[1] + aDirection[2]*aDirection[2] + aDirection[3]*aDirection[3])
            if nLength > 0 {
                aDirection[1] = aDirection[1] / nLength
                aDirection[2] = aDirection[2] / nLength
                aDirection[3] = aDirection[3] / nLength
                
                aDesiredPosition = [
                    aTargetPosition[1] + aDirection[1] * nMaxDistance,
                    aTargetPosition[2] + aDirection[2] * nMaxDistance,
                    aTargetPosition[3] + aDirection[3] * nMaxDistance
                ]
            }
        }
        
        # تطبيق حدود الارتفاع
        if aDesiredPosition[2] < nMinHeight {
            aDesiredPosition[2] = nMinHeight
        elseif aDesiredPosition[2] > nMaxHeight
            aDesiredPosition[2] = nMaxHeight
        }

    func updateThirdPerson nDeltaTime
        # تحديث كاميرا الشخص الثالث
        aCurrentPosition = oGameObject.getPosition()
        
        if bSmoothFollow {
            # حركة سلسة
            aCurrentPosition = smoothDamp(aCurrentPosition, aDesiredPosition, aCurrentVelocity, nSmoothTime, nDeltaTime)
        else
            # حركة مباشرة
            aCurrentPosition = [
                aCurrentPosition[1] + (aDesiredPosition[1] - aCurrentPosition[1]) * nFollowSpeed * nDeltaTime,
                aCurrentPosition[2] + (aDesiredPosition[2] - aCurrentPosition[2]) * nFollowSpeed * nDeltaTime,
                aCurrentPosition[3] + (aDesiredPosition[3] - aCurrentPosition[3]) * nFollowSpeed * nDeltaTime
            ]
        }
        
        # النظر نحو الهدف
        lookAtTarget()

    func updateFirstPerson nDeltaTime
        # تحديث كاميرا الشخص الأول
        aTargetPosition = oTarget.getPosition()
        aTargetRotation = oTarget.getRotation()
        
        # الكاميرا في نفس موقع ودوران الهدف
        aCurrentPosition = aTargetPosition
        oGameObject.setRotation(aTargetRotation)

    func updateOrbital nDeltaTime
        # تحديث الكاميرا المدارية
        # يمكن إضافة تحكم بالفأرة للدوران حول الهدف
        updateThirdPerson(nDeltaTime)

    func updateFree nDeltaTime
        # تحديث الكاميرا الحرة
        # تتبع أقل تقييداً
        aCurrentPosition = oGameObject.getPosition()
        
        aCurrentPosition = [
            aCurrentPosition[1] + (aDesiredPosition[1] - aCurrentPosition[1]) * nFollowSpeed * 0.5 * nDeltaTime,
            aCurrentPosition[2] + (aDesiredPosition[2] - aCurrentPosition[2]) * nFollowSpeed * 0.5 * nDeltaTime,
            aCurrentPosition[3] + (aDesiredPosition[3] - aCurrentPosition[3]) * nFollowSpeed * 0.5 * nDeltaTime
        ]

    func lookAtTarget
        # توجيه الكاميرا لتنظر نحو الهدف
        if oTarget = null {
            return
        }
        
        aTargetPosition = oTarget.getPosition()
        
        # إضافة مسافة النظر للأمام
        aTargetVelocity = [0, 0, 0]
        if oTarget.getComponent("RigidBody") != null {
            aTargetVelocity = oTarget.getComponent("RigidBody").getVelocity()
        }
        
        aLookAtPosition = [
            aTargetPosition[1] + aTargetVelocity[1] * nLookAheadDistance,
            aTargetPosition[2],
            aTargetPosition[3] + aTargetVelocity[3] * nLookAheadDistance
        ]
        
        # حساب اتجاه النظر
        aDirection = [
            aLookAtPosition[1] - aCurrentPosition[1],
            aLookAtPosition[2] - aCurrentPosition[2],
            aLookAtPosition[3] - aLookAtPosition[3]
        ]
        
        # حساب زوايا الدوران
        nYaw = atan2(aDirection[1], aDirection[3]) * 180.0 / 3.14159
        nPitch = atan2(aDirection[2], sqrt(aDirection[1]*aDirection[1] + aDirection[3]*aDirection[3])) * 180.0 / 3.14159
        
        aTargetRotation = [nPitch, nYaw, 0]
        
        if bSmoothRotation {
            # دوران سلس
            aCurrentRotation = oGameObject.getRotation()
            aNewRotation = [
                aCurrentRotation[1] + (aTargetRotation[1] - aCurrentRotation[1]) * nRotationSpeed * GetFrameTime(),
                aCurrentRotation[2] + (aTargetRotation[2] - aCurrentRotation[2]) * nRotationSpeed * GetFrameTime(),
                aCurrentRotation[3]
            ]
            oGameObject.setRotation(aNewRotation)
        else
            # دوران مباشر
            oGameObject.setRotation(aTargetRotation)
        }

    func startShake nIntensity, nDuration
        # بدء اهتزاز الكاميرا
        bShakeEnabled = true
        nShakeIntensity = nIntensity
        nShakeDuration = nDuration
        nShakeTimer = 0.0

    func updateShake nDeltaTime
        # تحديث اهتزاز الكاميرا
        if nShakeTimer < nShakeDuration {
            nShakeTimer += nDeltaTime
            
            # حساب شدة الاهتزاز المتناقصة
            nCurrentIntensity = nShakeIntensity * (1.0 - nShakeTimer / nShakeDuration)
            
            # إنشاء إزاحة عشوائية
            aShakeOffset = [
                (random(200) - 100) / 100.0 * nCurrentIntensity,
                (random(200) - 100) / 100.0 * nCurrentIntensity,
                (random(200) - 100) / 100.0 * nCurrentIntensity
            ]
        else
            # انتهاء الاهتزاز
            bShakeEnabled = false
            aShakeOffset = [0, 0, 0]
        }

    func applyFinalPosition
        # تطبيق الموقع النهائي مع الاهتزاز
        aFinalPosition = [
            aCurrentPosition[1] + aShakeOffset[1],
            aCurrentPosition[2] + aShakeOffset[2],
            aCurrentPosition[3] + aShakeOffset[3]
        ]
        
        oGameObject.setPosition(aFinalPosition)

    func smoothDamp aCurrentPos, aTargetPos, aCurrentVel, nSmoothTime, nDeltaTime
        # تطبيق تخميد سلس للحركة
        nOmega = 2.0 / nSmoothTime
        nX = nOmega * nDeltaTime
        nExp = 1.0 / (1.0 + nX + 0.48 * nX * nX + 0.235 * nX * nX * nX)
        
        aChange = [
            aCurrentPos[1] - aTargetPos[1],
            aCurrentPos[2] - aTargetPos[2],
            aCurrentPos[3] - aTargetPos[3]
        ]
        
        aTemp = [
            (aCurrentVel[1] + nOmega * aChange[1]) * nDeltaTime,
            (aCurrentVel[2] + nOmega * aChange[2]) * nDeltaTime,
            (aCurrentVel[3] + nOmega * aChange[3]) * nDeltaTime
        ]
        
        aCurrentVel[1] = (aCurrentVel[1] - nOmega * aTemp[1]) * nExp
        aCurrentVel[2] = (aCurrentVel[2] - nOmega * aTemp[2]) * nExp
        aCurrentVel[3] = (aCurrentVel[3] - nOmega * aTemp[3]) * nExp
        
        aResult = [
            aTargetPos[1] + (aChange[1] + aTemp[1]) * nExp,
            aTargetPos[2] + (aChange[2] + aTemp[2]) * nExp,
            aTargetPos[3] + (aChange[3] + aTemp[3]) * nExp
        ]
        
        return aResult

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    private
        oTarget
        aOffset
        nFollowSpeed
        nRotationSpeed
        nLookAheadDistance
        bSmoothFollow
        bSmoothRotation
        nSmoothTime
        nMinDistance
        nMaxDistance
        nMinHeight
        nMaxHeight
        bAvoidObstacles
        nCollisionRadius
        aCurrentVelocity
        aDesiredPosition
        aCurrentPosition
        bShakeEnabled
        nShakeIntensity
        nShakeDuration
        nShakeTimer
        aShakeOffset
        cFollowMode
}
