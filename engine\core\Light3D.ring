/*
الكلاس: Light3D
الوصف: نظام الإضاءة ثلاثية الأبعاد مع دعم أنواع مختلفة من الإضاءة
المدخلات: موقع الضوء، اللون، النوع، الشدة
المخرجات: إضاءة ثلاثية الأبعاد واقعية
*/

//load "raylib.ring"

# ثوابت أنواع الإضاءة
LIGHT_DIRECTIONAL = 0
LIGHT_POINT = 1
LIGHT_SPOT = 2

class Light3D {
    
    func init aPos, oCol, nType  #aPos = [0, 10, 0], oCol = WHITE, nType = LIGHT_DIRECTIONAL
        aPosition = aPos
        oColor = oCol
        nLightType = nType
        nIntensity = 1.0
        bEnabled = true
        
        # خصائص الضوء الاتجاهي
        aDirection = [0, -1, 0]
        
        # خصائص الضوء النقطي
        nRange = 100.0
        nAttenuation = 1.0
        
        # خصائص الضوء المخروطي
        nInnerCone = 30.0
        nOuterCone = 45.0
        
        # خصائص الظلال
        bCastShadows = true
        nShadowBias = 0.005
        nShadowMapSize = 1024
        
        # معرف فريد للضوء
        cID = generateLightID()
        
        setupLight()

    func generateLightID
        return "LIGHT_" + clock() + "_" + random(9999)

    func setupLight
        # إعداد الضوء حسب النوع
        switch nLightType
        on LIGHT_DIRECTIONAL
            setupDirectionalLight()
        on LIGHT_POINT
            setupPointLight()
        on LIGHT_SPOT
            setupSpotLight()
        off

    func setupDirectionalLight
        # إعداد الضوء الاتجاهي (مثل الشمس)
        # الضوء الاتجاهي لا يتأثر بالموقع، فقط بالاتجاه
        normalizeDirection()

    func setupPointLight
        # إعداد الضوء النقطي (مثل المصباح)
        # يضيء في جميع الاتجاهات من نقطة واحدة
        if nRange <= 0 {
            nRange = 100.0
        }

    func setupSpotLight
        # إعداد الضوء المخروطي (مثل الكشاف)
        # يضيء في مخروط من نقطة واحدة
        normalizeDirection()
        if nInnerCone >= nOuterCone {
            nInnerCone = nOuterCone - 5.0
        }

    func normalizeDirection
        # تطبيع اتجاه الضوء
        nLength = sqrt(aDirection[1]*aDirection[1] + 
                      aDirection[2]*aDirection[2] + 
                      aDirection[3]*aDirection[3])
        if nLength > 0 {
            aDirection[1] /= nLength
            aDirection[2] /= nLength
            aDirection[3] /= nLength
        }

    func setPosition aPos
        aPosition = aPos

    func getPosition
        return aPosition

    func setDirection aDir
        aDirection = aDir
        normalizeDirection()

    func getDirection
        return aDirection

    func setColor oCol
        oColor = oCol

    func getColor
        return oColor

    func setIntensity nInt
        nIntensity = nInt
        if nIntensity < 0 {
            nIntensity = 0
        }

    func getIntensity
        return nIntensity

    func setRange nRng
        nRange = nRng
        if nRange <= 0 {
            nRange = 1.0
        }

    func getRange
        return nRange

    func setAttenuation nAtt
        nAttenuation = nAtt

    func getAttenuation
        return nAttenuation

    func setInnerCone nAngle
        nInnerCone = nAngle
        if nInnerCone >= nOuterCone {
            nInnerCone = nOuterCone - 1.0
        }

    func getInnerCone
        return nInnerCone

    func setOuterCone nAngle
        nOuterCone = nAngle
        if nOuterCone <= nInnerCone {
            nOuterCone = nInnerCone + 1.0
        }

    func getOuterCone
        return nOuterCone

    func setEnabled bState
        bEnabled = bState

    func isEnabled
        return bEnabled

    func setCastShadows bState
        bCastShadows = bState

    func getCastShadows
        return bCastShadows

    func setShadowBias nBias
        nShadowBias = nBias

    func getShadowBias
        return nShadowBias

    func setShadowMapSize nSize
        nShadowMapSize = nSize

    func getShadowMapSize
        return nShadowMapSize

    func getLightType
        return nLightType

    func getID
        return cID

    func calculateLightContribution aWorldPos, aNormal
        # حساب مساهمة الضوء في نقطة معينة
        if not bEnabled {
            return [0, 0, 0]
        }
        
        switch nLightType
        on LIGHT_DIRECTIONAL
            return calculateDirectionalContribution(aWorldPos, aNormal)
        on LIGHT_POINT
            return calculatePointContribution(aWorldPos, aNormal)
        on LIGHT_SPOT
            return calculateSpotContribution(aWorldPos, aNormal)
        other
            return [0, 0, 0]
        off

    func calculateDirectionalContribution aWorldPos, aNormal
        # حساب الإضاءة الاتجاهية
        nDotProduct = dotProduct(aNormal, [-aDirection[1], -aDirection[2], -aDirection[3]])
        nDotProduct = max(nDotProduct, 0.0)
        
        nR = oColor.r / 255.0 * nIntensity * nDotProduct
        nG = oColor.g / 255.0 * nIntensity * nDotProduct
        nB = oColor.b / 255.0 * nIntensity * nDotProduct
        
        return [nR, nG, nB]

    func calculatePointContribution aWorldPos, aNormal
        # حساب الإضاءة النقطية
        aLightDir = [aPosition[1] - aWorldPos[1],
                     aPosition[2] - aWorldPos[2],
                     aPosition[3] - aWorldPos[3]]
        
        nDistance = vectorLength(aLightDir)
        if nDistance > nRange {
            return [0, 0, 0]
        }
        
        # تطبيع اتجاه الضوء
        aLightDir[1] /= nDistance
        aLightDir[2] /= nDistance
        aLightDir[3] /= nDistance
        
        nDotProduct = dotProduct(aNormal, aLightDir)
        nDotProduct = max(nDotProduct, 0.0)
        
        # حساب التخفيف
        nAttenFactor = 1.0 / (1.0 + nAttenuation * nDistance * nDistance / (nRange * nRange))
        
        nR = oColor.r / 255.0 * nIntensity * nDotProduct * nAttenFactor
        nG = oColor.g / 255.0 * nIntensity * nDotProduct * nAttenFactor
        nB = oColor.b / 255.0 * nIntensity * nDotProduct * nAttenFactor
        
        return [nR, nG, nB]

    func calculateSpotContribution aWorldPos, aNormal
        # حساب الإضاءة المخروطية
        aLightDir = [aPosition[1] - aWorldPos[1],
                     aPosition[2] - aWorldPos[2],
                     aPosition[3] - aWorldPos[3]]
        
        nDistance = vectorLength(aLightDir)
        if nDistance > nRange {
            return [0, 0, 0]
        }
        
        # تطبيع اتجاه الضوء
        aLightDir[1] /= nDistance
        aLightDir[2] /= nDistance
        aLightDir[3] /= nDistance
        
        # فحص الزاوية مع اتجاه الكشاف
        nSpotDot = dotProduct([-aLightDir[1], -aLightDir[2], -aLightDir[3]], aDirection)
        nSpotAngle = acos(nSpotDot) * 180.0 / 3.14159
        
        if nSpotAngle > nOuterCone {
            return [0, 0, 0]
        }
        
        # حساب تأثير المخروط
        nSpotFactor = 1.0
        if nSpotAngle > nInnerCone {
            nSpotFactor = (nOuterCone - nSpotAngle) / (nOuterCone - nInnerCone)
        }
        
        nDotProduct = dotProduct(aNormal, aLightDir)
        nDotProduct = max(nDotProduct, 0.0)
        
        # حساب التخفيف
        nAttenFactor = 1.0 / (1.0 + nAttenuation * nDistance * nDistance / (nRange * nRange))
        
        nR = oColor.r / 255.0 * nIntensity * nDotProduct * nAttenFactor * nSpotFactor
        nG = oColor.g / 255.0 * nIntensity * nDotProduct * nAttenFactor * nSpotFactor
        nB = oColor.b / 255.0 * nIntensity * nDotProduct * nAttenFactor * nSpotFactor
        
        return [nR, nG, nB]

    func dotProduct aVec1, aVec2
        return aVec1[1]*aVec2[1] + aVec1[2]*aVec2[2] + aVec1[3]*aVec2[3]

    func vectorLength aVec
        return sqrt(aVec[1]*aVec[1] + aVec[2]*aVec[2] + aVec[3]*aVec[3])

    func max nA, nB
        if nA > nB {
            return nA
        }
        return nB

    func render
        # رسم تمثيل مرئي للضوء (للتصحيح)
        if not bEnabled {
            return
        }
        
        switch nLightType
        on LIGHT_DIRECTIONAL
            # رسم خط يمثل الاتجاه
            aEndPos = [aPosition[1] + aDirection[1] * 5,
                       aPosition[2] + aDirection[2] * 5,
                       aPosition[3] + aDirection[3] * 5]
            DrawLine3D(aPosition, aEndPos, oColor)
        on LIGHT_POINT
            # رسم كرة صغيرة
            DrawSphere(aPosition, 0.5, oColor)
        on LIGHT_SPOT
            # رسم مخروط صغير
            DrawSphere(aPosition, 0.3, oColor)
            aEndPos = [aPosition[1] + aDirection[1] * 3,
                       aPosition[2] + aDirection[2] * 3,
                       aPosition[3] + aDirection[3] * 3]
            DrawLine3D(aPosition, aEndPos, oColor)
        off

    private
        cID
        aPosition = [0, 10, 0]
        aDirection
        oColor = WHITE
        nLightType = LIGHT_DIRECTIONAL
        nIntensity
        bEnabled
        nRange
        nAttenuation
        nInnerCone
        nOuterCone
        bCastShadows
        nShadowBias
        nShadowMapSize
}
