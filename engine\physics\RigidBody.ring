/*
الكلاس: RigidBody
الوصف: مكون الجسم الصلب للفيزياء
المدخلات: خصائص فيزيائية للكائن
المخرجات: محاكاة فيزيائية واقعية
*/

//load "../core/Component.ring"

class RigidBody from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("RigidBody")
        
        # الخصائص الأساسية
        nMass = 1.0
        bIsStatic = false
        bIsKinematic = false
        bUseGravity = true
        
        # الحركة الخطية
        aVelocity = [0.0, 0.0, 0.0]
        aAcceleration = [0.0, 0.0, 0.0]
        aForces = [0.0, 0.0, 0.0]
        
        # الحركة الدورانية
        aAngularVelocity = [0.0, 0.0, 0.0]
        aAngularAcceleration = [0.0, 0.0, 0.0]
        aTorque = [0.0, 0.0, 0.0]
        
        # المواد الفيزيائية
        nRestitution = 0.5      # الارتداد (0 = لا ارتداد, 1 = ارتداد كامل)
        nFriction = 0.7         # الاحتكاك
        nLinearDamping = 0.1    # تخميد الحركة الخطية
        nAngularDamping = 0.1   # تخميد الحركة الدورانية
        
        # القيود
        bFreezePositionX = false
        bFreezePositionY = false
        bFreezePositionZ = false
        bFreezeRotationX = false
        bFreezeRotationY = false
        bFreezeRotationZ = false
        
        # الحالة
        bIsAwake = true
        bIsSleeping = false
        nSleepThreshold = 0.1
        nSleepTimer = 0.0
        
        # مقياس الجاذبية
        nGravityScale = 1.0
        
        # الموقع والدوران السابق
        aPreviousPosition = [0, 0, 0]
        aPreviousRotation = [0, 0, 0]

    func setMass nNewMass
        if nNewMass > 0 {
            nMass = nNewMass
        }

    func getMass
        return nMass

    func setStatic bState
        bIsStatic = bState
        if bIsStatic {
            aVelocity = [0, 0, 0]
            aAngularVelocity = [0, 0, 0]
            aForces = [0, 0, 0]
            aTorque = [0, 0, 0]
        }

    func isStatic
        return bIsStatic

    func setKinematic bState
        bIsKinematic = bState

    func isKinematic
        return bIsKinematic

    func setUseGravity bState
        bUseGravity = bState

    func getUseGravity
        return bUseGravity

    func setVelocity aNewVelocity
        if not bIsStatic {
            aVelocity = aNewVelocity
            wakeUp()
        }

    func getVelocity
        return aVelocity

    func setAngularVelocity aNewAngularVelocity
        if not bIsStatic {
            aAngularVelocity = aNewAngularVelocity
            wakeUp()
        }

    func getAngularVelocity
        return aAngularVelocity

    func addForce aForce
        if not bIsStatic and not bIsKinematic {
            aForces[1] += aForce[1]
            aForces[2] += aForce[2]
            aForces[3] += aForce[3]
            wakeUp()
        }

    func addTorque aTorqueForce
        if not bIsStatic and not bIsKinematic {
            aTorque[1] += aTorqueForce[1]
            aTorque[2] += aTorqueForce[2]
            aTorque[3] += aTorqueForce[3]
            wakeUp()
        }

    func addForceAtPosition aForce, aPosition
        # إضافة قوة في موقع معين (ينتج عنها عزم دوران)
        if not bIsStatic and not bIsKinematic {
            addForce(aForce)
            
            # حساب العزم الناتج
            if oGameObject != null {
                aCenterOfMass = oGameObject.getPosition()
                
                # متجه من مركز الكتلة لنقطة التطبيق
                aR = [
                    aPosition[1] - aCenterOfMass[1],
                    aPosition[2] - aCenterOfMass[2],
                    aPosition[3] - aCenterOfMass[3]
                ]
                
                # حساب الضرب الاتجاهي (Cross Product)
                aCrossTorque = [
                    aR[2] * aForce[3] - aR[3] * aForce[2],
                    aR[3] * aForce[1] - aR[1] * aForce[3],
                    aR[1] * aForce[2] - aR[2] * aForce[1]
                ]
                
                addTorque(aCrossTorque)
            }
        }

    func setRestitution nValue
        nRestitution = max(0, min(1, nValue))

    func getRestitution
        return nRestitution

    func setFriction nValue
        nFriction = max(0, nValue)

    func getFriction
        return nFriction

    func setLinearDamping nValue
        nLinearDamping = max(0, nValue)

    func getLinearDamping
        return nLinearDamping

    func setAngularDamping nValue
        nAngularDamping = max(0, nValue)

    func getAngularDamping
        return nAngularDamping

    func freezePosition bX, bY, bZ
        bFreezePositionX = bX
        bFreezePositionY = bY
        bFreezePositionZ = bZ

    func freezeRotation bX, bY, bZ
        bFreezeRotationX = bX
        bFreezeRotationY = bY
        bFreezeRotationZ = bZ

    func setGravityScale nScale
        nGravityScale = nScale

    func getGravityScale
        return nGravityScale

    func wakeUp
        bIsAwake = true
        bIsSleeping = false
        nSleepTimer = 0.0

    func setAwake bState
        if bState {
            wakeUp()
        else
            bIsAwake = false
            bIsSleeping = true
        }

    func isAwake
        return bIsAwake

    func isSleeping
        return bIsSleeping

    func update nDeltaTime
        # تحديث الفيزياء
        if bIsStatic or bIsSleeping {
            return
        }
        
        # حفظ الموقع السابق
        if oGameObject != null {
            aPreviousPosition = oGameObject.getPosition()
            aPreviousRotation = oGameObject.getRotation()
        }
        
        # تطبيق الجاذبية
        if bUseGravity and not bIsKinematic {
            nGravity = -9.81 * nGravityScale
            addForce([0, nGravity * nMass, 0])
        }
        
        # حساب التسارع من القوى
        if not bIsKinematic and nMass > 0 {
            aAcceleration = [
                aForces[1] / nMass,
                aForces[2] / nMass,
                aForces[3] / nMass
            ]
            
            aAngularAcceleration = [
                aTorque[1] / nMass,  # تبسيط - يجب استخدام moment of inertia
                aTorque[2] / nMass,
                aTorque[3] / nMass
            ]
        }
        
        # تحديث السرعة
        aVelocity[1] += aAcceleration[1] * nDeltaTime
        aVelocity[2] += aAcceleration[2] * nDeltaTime
        aVelocity[3] += aAcceleration[3] * nDeltaTime
        
        aAngularVelocity[1] += aAngularAcceleration[1] * nDeltaTime
        aAngularVelocity[2] += aAngularAcceleration[2] * nDeltaTime
        aAngularVelocity[3] += aAngularAcceleration[3] * nDeltaTime
        
        # تطبيق التخميد
        aVelocity[1] *= (1.0 - nLinearDamping * nDeltaTime)
        aVelocity[2] *= (1.0 - nLinearDamping * nDeltaTime)
        aVelocity[3] *= (1.0 - nLinearDamping * nDeltaTime)
        
        aAngularVelocity[1] *= (1.0 - nAngularDamping * nDeltaTime)
        aAngularVelocity[2] *= (1.0 - nAngularDamping * nDeltaTime)
        aAngularVelocity[3] *= (1.0 - nAngularDamping * nDeltaTime)
        
        # تطبيق القيود
        if bFreezePositionX { aVelocity[1] = 0 }
        if bFreezePositionY { aVelocity[2] = 0 }
        if bFreezePositionZ { aVelocity[3] = 0 }
        
        if bFreezeRotationX { aAngularVelocity[1] = 0 }
        if bFreezeRotationY { aAngularVelocity[2] = 0 }
        if bFreezeRotationZ { aAngularVelocity[3] = 0 }
        
        # تحديث الموقع
        if oGameObject != null and not bIsKinematic {
            aCurrentPos = oGameObject.getPosition()
            aNewPos = [
                aCurrentPos[1] + aVelocity[1] * nDeltaTime,
                aCurrentPos[2] + aVelocity[2] * nDeltaTime,
                aCurrentPos[3] + aVelocity[3] * nDeltaTime
            ]
            oGameObject.setPosition(aNewPos)
            
            aCurrentRot = oGameObject.getRotation()
            aNewRot = [
                aCurrentRot[1] + aAngularVelocity[1] * nDeltaTime,
                aCurrentRot[2] + aAngularVelocity[2] * nDeltaTime,
                aCurrentRot[3] + aAngularVelocity[3] * nDeltaTime
            ]
            oGameObject.setRotation(aNewRot)
        }
        
        # إعادة تعيين القوى
        aForces = [0, 0, 0]
        aTorque = [0, 0, 0]
        
        # فحص النوم
        checkSleep(nDeltaTime)

    func checkSleep nDeltaTime
        # فحص إذا كان الجسم يجب أن ينام
        nVelocityMagnitude = sqrt(aVelocity[1]*aVelocity[1] + aVelocity[2]*aVelocity[2] + aVelocity[3]*aVelocity[3])
        nAngularMagnitude = sqrt(aAngularVelocity[1]*aAngularVelocity[1] + aAngularVelocity[2]*aAngularVelocity[2] + aAngularVelocity[3]*aAngularVelocity[3])
        
        if nVelocityMagnitude < nSleepThreshold and nAngularMagnitude < nSleepThreshold {
            nSleepTimer += nDeltaTime
            if nSleepTimer > 1.0 {  # ينام بعد ثانية واحدة من عدم الحركة
                bIsSleeping = true
                bIsAwake = false
            }
        else
            nSleepTimer = 0.0
        }

    func onCollision oOtherRigidBody, aContactPoint, aContactNormal
        # معالجة التصادم مع جسم صلب آخر
        if bIsStatic or oOtherRigidBody = null {
            return
        }
        
        # حساب السرعة النسبية
        aOtherVelocity = oOtherRigidBody.getVelocity()
        aRelativeVelocity = [
            aVelocity[1] - aOtherVelocity[1],
            aVelocity[2] - aOtherVelocity[2],
            aVelocity[3] - aOtherVelocity[3]
        ]
        
        # حساب السرعة في اتجاه التصادم
        nContactVelocity = aRelativeVelocity[1] * aContactNormal[1] + 
                          aRelativeVelocity[2] * aContactNormal[2] + 
                          aRelativeVelocity[3] * aContactNormal[3]
        
        # إذا كانت الكائنات تتحرك بعيداً عن بعضها، لا نحتاج لحل التصادم
        if nContactVelocity > 0 {
            return
        }
        
        # حساب معامل الارتداد المشترك
        nCombinedRestitution = min(nRestitution, oOtherRigidBody.getRestitution())
        
        # حساب قوة الدفع
        nImpulseMagnitude = -(1 + nCombinedRestitution) * nContactVelocity
        nImpulseMagnitude = nImpulseMagnitude / (1/nMass + 1/oOtherRigidBody.getMass())
        
        aImpulse = [
            aContactNormal[1] * nImpulseMagnitude,
            aContactNormal[2] * nImpulseMagnitude,
            aContactNormal[3] * nImpulseMagnitude
        ]
        
        # تطبيق الدفع
        aVelocity[1] += aImpulse[1] / nMass
        aVelocity[2] += aImpulse[2] / nMass
        aVelocity[3] += aImpulse[3] / nMass
        
        wakeUp()

    private
        nMass
        bIsStatic
        bIsKinematic
        bUseGravity
        aVelocity
        aAcceleration
        aForces
        aAngularVelocity
        aAngularAcceleration
        aTorque
        nRestitution
        nFriction
        nLinearDamping
        nAngularDamping
        bFreezePositionX
        bFreezePositionY
        bFreezePositionZ
        bFreezeRotationX
        bFreezeRotationY
        bFreezeRotationZ
        bIsAwake
        bIsSleeping
        nSleepThreshold
        nSleepTimer
        nGravityScale
        aPreviousPosition
        aPreviousRotation
}
