class StorySystem {
    

    func init
        oQuestManager = new QuestManager
        oDialogueUI = new DialogueUI
        initializeVariables()

    func initializeVariables
        # متغيرات القصة الأساسية
        setVariable("playerName", "")
        setVariable("playerLevel", 1)
        setVariable("reputation", 0)
        setVariable("completedQuests", [])

    func loadStory cPath
        try {
            cContent = read(cPath)
            oData = JSON2list(cContent)
            
            # تحميل الحوارات
            for oDialogueData in oData["dialogues"] {
                loadDialogue(oDialogueData)
            }
            
            # تحميل المهام
            for oQuestData in oData["quests"] {
                loadQuest(oQuestData)
            }
            
            return true
        catch 
            log("Error loading story: " + cCatchError)
            return false
        }

    func loadDialogue oData
        oDialogue = new Dialogue {
            cID = oData["id"]
            cSpeaker = oData["speaker"]
            aLines = oData["lines"]
            aChoices = oData["choices"]
            aConditions = oData["conditions"]
        }
        add(aDialogues, oDialogue)

    func loadQuest oData
        oQuest = new Quest {
            cID = oData["id"]
            cTitle = oData["title"]
            cDescription = oData["description"]
            aObjectives = oData["objectives"]
            xReward = oData["reward"]
            aRequirements = oData["requirements"]
        }
        oQuestManager.addQuest(oQuest)

    func startDialogue cDialogueID
        oDialogue = findDialogue(cDialogueID)
        if oDialogue and checkDialogueConditions(oDialogue) {
            oCurrentDialogue = oDialogue
            oDialogueUI.showDialogue(oDialogue)
            return true
        }
        return false

    func makeChoice nChoiceIndex
        if not oCurrentDialogue {
            return false
        }
        
        oChoice = oCurrentDialogue.getChoice(nChoiceIndex)
        if oChoice {
            # تنفيذ تأثيرات الاختيار
            executeChoiceEffects(oChoice)
            
            # الانتقال إلى الحوار التالي إذا وجد
            if oChoice.nextDialogue {
                return startDialogue(oChoice.nextDialogue)
            }
            
            endDialogue()
            return true
        }
        return false

    func startQuest cQuestID
        oQuest = oQuestManager.getQuest(cQuestID)
        if oQuest and checkQuestRequirements(oQuest) {
            oQuestManager.activateQuest(oQuest)
            return true
        }
        return false

    func completeObjective cQuestID, cObjectiveID
        oQuest = oQuestManager.getQuest(cQuestID)
        if oQuest {
            if oQuest.completeObjective(cObjectiveID) {
                checkQuestCompletion(oQuest)
            }
        }

    func checkQuestCompletion oQuest
        if oQuest.isCompleted() {
            giveQuestReward(oQuest)
            oQuestManager.completeQuest(oQuest)
            add(getVariable("completedQuests"), oQuest.cID)
        }

    func setVariable cName, xValue
        aVariables[cName] = xValue

    func getVariable cName
        return aVariables[cName]

    private
        aQuests = []
        aDialogues = []
        aChoices = []
        aVariables = []
        oCurrentDialogue
        oQuestManager
        oDialogueUI

    func findDialogue cID
        for oDialogue in aDialogues {
            if oDialogue.cID = cID {
                return oDialogue
            }
        }
        return null

    func checkDialogueConditions oDialogue
        for oCondition in oDialogue.aConditions {
            if not evaluateCondition(oCondition) {
                return false
            }
        }
        return true

    func evaluateCondition oCondition
        switch oCondition.type {
            case "variable"
                xValue = getVariable(oCondition.variable)
                return compareValues(xValue, oCondition.value, 
                                  oCondition.operator)
            
            case "quest"
                return checkQuestStatus(oCondition.questID, 
                                     oCondition.status)
            
            case "level"
                return getVariable("playerLevel") >= oCondition.level
            
            case "reputation"
                return getVariable("reputation") >= oCondition.value
        }
        return false

    func executeChoiceEffects oChoice
        for oEffect in oChoice.effects {
            switch oEffect.type {
                case "variable"
                    setVariable(oEffect.variable, oEffect.value)
                
                case "quest"
                    startQuest(oEffect.questID)
                
                case "reputation"
                    nCurrentRep = getVariable("reputation")
                    setVariable("reputation", nCurrentRep + oEffect.value)
                
                case "item"
                    addItemToInventory(oEffect.itemID, oEffect.quantity)
            }
        }

    func checkQuestRequirements oQuest
        for oReq in oQuest.aRequirements {
            if not evaluateCondition(oReq) {
                return false
            }
        }
        return true

    func giveQuestReward oQuest
        # منح المكافآت
        if type(oQuest.xReward) = "NUMBER" {
            addGold(oQuest.xReward)
        else
            for cItem in oQuest.xReward {
                for nQuantity in oQuest.xReward {
                    addItemToInventory(cItem, nQuantity)
                }
            }
        }
}
