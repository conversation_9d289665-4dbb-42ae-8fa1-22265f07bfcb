/*
الكلاس: AIAgent
الوصف: وكيل ذكي للكائنات غير اللاعب
المدخلات: نوع الوكيل وإعداداته
المخرجات: سلوك ذكي للكائن
*/

/* load "../core/Component.ring"
load "BehaviorTree.ring"
load "FiniteStateMachine.ring"
 */
class AIAgent from Component {
    
    func init cAgentType    # = "basic"
        # استدعاء منشئ الكلاس الأساسي
        super.init("AIAgent")
        
        # نوع الوكيل
        cType = cAgentType
        
        # خصائص الإدراك
        nVisionRange = 10.0
        nHearingRange = 15.0
        nVisionAngle = 90.0  # بالدرجات
        
        # خصائص الحركة
        nMovementSpeed = 3.0
        nRotationSpeed = 180.0  # درجة/ثانية
        nStoppingDistance = 1.0
        
        # الحالة الحالية
        cCurrentState = "idle"
        oTarget = null
        aDestination = [0, 0, 0]
        bHasDestination = false
        
        # نظام الحالة المحدودة
        oStateMachine = new FiniteStateMachine()
        
        # شجرة السلوك
        oBehaviorTree = new BehaviorTree()
        
        # قائمة الأهداف المكتشفة
        aDetectedTargets = []
        aKnownTargets = []
        
        # نقاط الدورية
        aPatrolPoints = []
        nCurrentPatrolIndex = 0
        
        # متغيرات التوقيت
        nLastUpdateTime = 0
        nDecisionInterval = 0.1  # تحديث القرارات كل 0.1 ثانية
        
        # إعداد السلوك حسب النوع
        setupBehavior()

    func setupBehavior
        # إعداد السلوك حسب نوع الوكيل
        switch cType
        on "guard"
            setupGuardBehavior()
        on "patrol"
            setupPatrolBehavior()
        on "hunter"
            setupHunterBehavior()
        on "civilian"
            setupCivilianBehavior()
        other
            setupBasicBehavior()
        off

    func setupGuardBehavior
        # سلوك الحارس
        nVisionRange = 12.0
        nHearingRange = 18.0
        nMovementSpeed = 4.0
        
        # إعداد حالات الحارس
        oStateMachine.addState("idle", method(:guardIdle))
        oStateMachine.addState("alert", method(:guardAlert))
        oStateMachine.addState("chase", method(:guardChase))
        oStateMachine.addState("search", method(:guardSearch))
        oStateMachine.addState("return", method(:guardReturn))
        
        oStateMachine.setState("idle")

    func setupPatrolBehavior
        # سلوك الدورية
        nVisionRange = 8.0
        nMovementSpeed = 2.5
        
        oStateMachine.addState("patrol", method(:patrolMove))
        oStateMachine.addState("investigate", method(:patrolInvestigate))
        oStateMachine.addState("alert", method(:patrolAlert))
        
        oStateMachine.setState("patrol")

    func setupHunterBehavior
        # سلوك الصياد
        nVisionRange = 15.0
        nHearingRange = 20.0
        nMovementSpeed = 5.0
        
        oStateMachine.addState("hunt", method(:hunterHunt))
        oStateMachine.addState("stalk", method(:hunterStalk))
        oStateMachine.addState("attack", method(:hunterAttack))
        oStateMachine.addState("retreat", method(:hunterRetreat))
        
        oStateMachine.setState("hunt")

    func setupCivilianBehavior
        # سلوك المدني
        nVisionRange = 6.0
        nMovementSpeed = 2.0
        
        oStateMachine.addState("wander", method(:civilianWander))
        oStateMachine.addState("flee", method(:civilianFlee))
        oStateMachine.addState("hide", method(:civilianHide))
        
        oStateMachine.setState("wander")

    func setupBasicBehavior
        # سلوك أساسي
        oStateMachine.addState("idle", method(:basicIdle))
        oStateMachine.setState("idle")

    func setVisionRange nRange
        nVisionRange = nRange

    func getVisionRange
        return nVisionRange

    func setHearingRange nRange
        nHearingRange = nRange

    func getHearingRange
        return nHearingRange

    func setMovementSpeed nSpeed
        nMovementSpeed = nSpeed

    func getMovementSpeed
        return nMovementSpeed

    func setTarget oNewTarget
        oTarget = oNewTarget

    func getTarget
        return oTarget

    func addPatrolPoint aPoint
        add(aPatrolPoints, aPoint)

    func setPatrolPoints aPoints
        aPatrolPoints = aPoints
        nCurrentPatrolIndex = 0

    func update nDeltaTime
        nLastUpdateTime += nDeltaTime
        
        # تحديث القرارات بفترات منتظمة
        if nLastUpdateTime >= nDecisionInterval {
            # فحص البيئة
            perceiveEnvironment()
            
            # تحديث آلة الحالة
            oStateMachine.update(nDeltaTime)
            
            nLastUpdateTime = 0
        }
        
        # تحديث الحركة
        updateMovement(nDeltaTime)

    func perceiveEnvironment
        # فحص البيئة واكتشاف الأهداف
        aDetectedTargets = []
        
        if oGameObject = null {
            return
        }
        
        aMyPosition = oGameObject.getPosition()
        
        # البحث عن الأهداف في نطاق الرؤية
        # هذا يتطلب نظام للبحث عن الكائنات القريبة
        # للآن سنستخدم فحص بسيط
        
        # فحص اللاعب إذا كان موجود
        if oTarget != null {
            aTargetPos = oTarget.getPosition()
            nDistance = calculateDistance(aMyPosition, aTargetPos)
            
            if nDistance <= nVisionRange {
                if canSeeTarget(oTarget) {
                    add(aDetectedTargets, oTarget)
                    updateKnownTarget(oTarget)
                }
            elseif nDistance <= nHearingRange
                # يمكن سماع الهدف
                if canHearTarget(oTarget) {
                    updateKnownTarget(oTarget)
                }
            }
        }

    func canSeeTarget oTargetObject
        # فحص إذا كان يمكن رؤية الهدف
        if oTargetObject = null or oGameObject = null {
            return false
        }
        
        aMyPosition = oGameObject.getPosition()
        aTargetPos = oTargetObject.getPosition()
        
        # حساب الاتجاه للهدف
        aDirection = [
            aTargetPos[1] - aMyPosition[1],
            aTargetPos[2] - aMyPosition[2],
            aTargetPos[3] - aTargetPos[3]
        ]
        
        # تطبيع الاتجاه
        nDistance = sqrt(aDirection[1]*aDirection[1] + aDirection[2]*aDirection[2] + aDirection[3]*aDirection[3])
        if nDistance = 0 {
            return false
        }
        
        aDirection[1] = aDirection[1] / nDistance
        aDirection[2] = aDirection[2] / nDistance
        aDirection[3] = aDirection[3] / nDistance
        
        # فحص زاوية الرؤية
        aMyRotation = oGameObject.getRotation()
        aForward = [0, 0, -1]  # الاتجاه الأمامي الافتراضي
        
        # حساب الضرب النقطي
        nDotProduct = aDirection[1] * aForward[1] + aDirection[2] * aForward[2] + aDirection[3] * aForward[3]
        nAngle = acos(nDotProduct) * 180.0 / 3.14159
        
        return nAngle <= (nVisionAngle / 2.0)

    func canHearTarget oTargetObject
        # فحص إذا كان يمكن سماع الهدف
        # للآن سنعتبر أن جميع الأهداف مسموعة في نطاق السمع
        return true

    func updateKnownTarget oTargetObject
        # تحديث معلومات الهدف المعروف
        aTargetPos = oTargetObject.getPosition()
        nCurrentTime = GetTime()
        
        # البحث عن الهدف في القائمة
        bFound = false
        for i = 1 to len(aKnownTargets) {
            if aKnownTargets[i][1] = oTargetObject {
                aKnownTargets[i][2] = aTargetPos
                aKnownTargets[i][3] = nCurrentTime
                bFound = true
                exit
            }
        }
        
        # إضافة هدف جديد
        if not bFound {
            add(aKnownTargets, [oTargetObject, aTargetPos, nCurrentTime])
        }

    func updateMovement nDeltaTime
        # تحديث حركة الوكيل
        if not bHasDestination or oGameObject = null {
            return
        }
        
        aCurrentPos = oGameObject.getPosition()
        
        # حساب الاتجاه للوجهة
        aDirection = [
            aDestination[1] - aCurrentPos[1],
            0,  # تجاهل المحور Y للحركة الأفقية
            aDestination[3] - aCurrentPos[3]
        ]
        
        nDistance = sqrt(aDirection[1]*aDirection[1] + aDirection[3]*aDirection[3])
        
        # فحص الوصول للوجهة
        if nDistance <= nStoppingDistance {
            bHasDestination = false
            onDestinationReached()
            return
        }
        
        # تطبيع الاتجاه
        aDirection[1] = aDirection[1] / nDistance
        aDirection[3] = aDirection[3] / nDistance
        
        # تحديث الموقع
        aNewPos = [
            aCurrentPos[1] + aDirection[1] * nMovementSpeed * nDeltaTime,
            aCurrentPos[2],
            aCurrentPos[3] + aDirection[3] * nMovementSpeed * nDeltaTime
        ]
        
        oGameObject.setPosition(aNewPos)
        
        # تحديث الدوران ليواجه اتجاه الحركة
        nTargetYaw = atan2(aDirection[1], aDirection[3]) * 180.0 / 3.14159
        aCurrentRotation = oGameObject.getRotation()
        
        # دوران تدريجي
        nYawDiff = nTargetYaw - aCurrentRotation[2]
        if nYawDiff > 180 { nYawDiff -= 360 }
        if nYawDiff < -180 { nYawDiff += 360 }
        
        nMaxRotation = nRotationSpeed * nDeltaTime
        if abs(nYawDiff) > nMaxRotation {
            if nYawDiff > 0 {
                nTargetYaw = aCurrentRotation[2] + nMaxRotation
            else
                nTargetYaw = aCurrentRotation[2] - nMaxRotation
            }
        }
        
        oGameObject.setRotation([aCurrentRotation[1], nTargetYaw, aCurrentRotation[3]])

    func moveTo aPosition
        aDestination = aPosition
        bHasDestination = true

    func onDestinationReached
        # استدعى عند الوصول للوجهة
        # يمكن للكلاسات المشتقة تخصيص هذا السلوك

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    # حالات الحارس
    func guardIdle nDeltaTime
        if len(aDetectedTargets) > 0 {
            oStateMachine.setState("alert")
        }

    func guardAlert nDeltaTime
        if len(aDetectedTargets) > 0 {
            oTarget = aDetectedTargets[1]
            oStateMachine.setState("chase")
        }

    func guardChase nDeltaTime
        if oTarget != null {
            moveTo(oTarget.getPosition())
        }

    func guardSearch nDeltaTime
        # البحث عن الهدف المفقود

    func guardReturn nDeltaTime
        # العودة للموقع الأصلي

    # حالات الدورية
    func patrolMove nDeltaTime
        if len(aPatrolPoints) > 0 {
            if not bHasDestination {
                moveTo(aPatrolPoints[nCurrentPatrolIndex + 1])
            }
        }

    func patrolInvestigate nDeltaTime
        # التحقق من شيء مشبوه

    func patrolAlert nDeltaTime
        # حالة التأهب

    # حالات الصياد
    func hunterHunt nDeltaTime
        # البحث عن فريسة

    func hunterStalk nDeltaTime
        # تتبع الفريسة

    func hunterAttack nDeltaTime
        # مهاجمة الفريسة

    func hunterRetreat nDeltaTime
        # التراجع

    # حالات المدني
    func civilianWander nDeltaTime
        # التجول العشوائي
        if not bHasDestination {
            # اختيار وجهة عشوائية
            aRandomDest = [
                random(20) - 10,
                0,
                random(20) - 10
            ]
            moveTo(aRandomDest)
        }

    func civilianFlee nDeltaTime
        # الهروب من الخطر

    func civilianHide nDeltaTime
        # الاختباء

    # حالة أساسية
    func basicIdle nDeltaTime
        # لا يفعل شيء

    private
        cType  = "basic"
        nVisionRange
        nHearingRange
        nVisionAngle
        nMovementSpeed
        nRotationSpeed
        nStoppingDistance
        cCurrentState
        oTarget
        aDestination
        bHasDestination
        oStateMachine
        oBehaviorTree
        aDetectedTargets
        aKnownTargets
        aPatrolPoints
        nCurrentPatrolIndex
        nLastUpdateTime
        nDecisionInterval
}
