/*
الكلاس: لGameMaterial
الوصف: نظام المواد والقوام للكائنات ثلاثية الأبعاد
المدخلات: قوام، ألوان، خصائص المادة
المخرجات: مادة قابلة للتطبيق على النماذج
*/

//load "raylib.ring"

class GameMaterial {
    
    func init cMaterialName    # = "DefaultMaterial"
        cName = cMaterialName
        cID = generateMaterialID()
        
        # الألوان الأساسية
        oAlbedoColor = WHITE
        oEmissiveColor = BLACK
        oSpecularColor = WHITE
        
        # القوام
        oAlbedoTexture = null
        oNormalTexture = null
        oSpecularTexture = null
        oEmissiveTexture = null
        oRoughnessTexture = null
        oMetallicTexture = null
        oAOTexture = null  # Ambient Occlusion
        
        # خصائص المادة
        nRoughness = 0.5
        nMetallic = 0.0
        nSpecularPower = 32.0
        nEmissiveStrength = 0.0
        nAlpha = 1.0
        nRefractionIndex = 1.0
        
        # خصائص التكرار
        nTextureScaleU = 1.0
        nTextureScaleV = 1.0
        nTextureOffsetU = 0.0
        nTextureOffsetV = 0.0
        
        # خصائص الشفافية
        bTransparent = false
        nBlendMode = BLEND_ALPHA
        
        # خصائص أخرى
        bDoubleSided = false
        bCastShadows = true
        bReceiveShadows = true
        
        setupDefaultMaterial()

    func generateMaterialID
        return "MAT_" + string(clock()) + "_" + string(random(9999))

    func setupDefaultMaterial
        # إعداد المادة الافتراضية
        if nAlpha < 1.0 {
            bTransparent = true
        }

    func setAlbedoColor oColor
        oAlbedoColor = oColor

    func getAlbedoColor
        return oAlbedoColor

    func setEmissiveColor oColor
        oEmissiveColor = oColor

    func getEmissiveColor
        return oEmissiveColor

    func setSpecularColor oColor
        oSpecularColor = oColor

    func getSpecularColor
        return oSpecularColor

    func setAlbedoTexture oTexture
        oAlbedoTexture = oTexture

    func getAlbedoTexture
        return oAlbedoTexture

    func setNormalTexture oTexture
        oNormalTexture = oTexture

    func getNormalTexture
        return oNormalTexture

    func setSpecularTexture oTexture
        oSpecularTexture = oTexture

    func getSpecularTexture
        return oSpecularTexture

    func setEmissiveTexture oTexture
        oEmissiveTexture = oTexture

    func getEmissiveTexture
        return oEmissiveTexture

    func setRoughnessTexture oTexture
        oRoughnessTexture = oTexture

    func getRoughnessTexture
        return oRoughnessTexture

    func setMetallicTexture oTexture
        oMetallicTexture = oTexture

    func getMetallicTexture
        return oMetallicTexture

    func setAOTexture oTexture
        oAOTexture = oTexture

    func getAOTexture
        return oAOTexture

    func setRoughness nValue
        nRoughness = clamp(nValue, 0.0, 1.0)

    func getRoughness
        return nRoughness

    func setMetallic nValue
        nMetallic = clamp(nValue, 0.0, 1.0)

    func getMetallic
        return nMetallic

    func setSpecularPower nValue
        nSpecularPower = max(nValue, 1.0)

    func getSpecularPower
        return nSpecularPower

    func setEmissiveStrength nValue
        nEmissiveStrength = max(nValue, 0.0)

    func getEmissiveStrength
        return nEmissiveStrength

    func setAlpha nValue
        nAlpha = clamp(nValue, 0.0, 1.0)
        if nAlpha < 1.0 {
            bTransparent = true
        else
            bTransparent = false
        }

    func getAlpha
        return nAlpha

    func setTextureScale nU, nV
        nTextureScaleU = nU
        nTextureScaleV = nV

    func getTextureScaleU
        return nTextureScaleU

    func getTextureScaleV
        return nTextureScaleV

    func setTextureOffset nU, nV
        nTextureOffsetU = nU
        nTextureOffsetV = nV

    func getTextureOffsetU
        return nTextureOffsetU

    func getTextureOffsetV
        return nTextureOffsetV

    func setTransparent bState
        bTransparent = bState

    func isTransparent
        return bTransparent

    func setBlendMode nMode
        nBlendMode = nMode

    func getBlendMode
        return nBlendMode

    func setDoubleSided bState
        bDoubleSided = bState

    func isDoubleSided
        return bDoubleSided

    func setCastShadows bState
        bCastShadows = bState

    func getCastShadows
        return bCastShadows

    func setReceiveShadows bState
        bReceiveShadows = bState

    func getReceiveShadows
        return bReceiveShadows

    func apply
        # تطبيق المادة على النموذج الحالي
        
        # تطبيق القوام الأساسي
        if oAlbedoTexture != null {
            # تطبيق قوام الألوان
            # ملاحظة: هذا يتطلب دعم من RayLib للمواد المتقدمة
        }
        
        # تطبيق خصائص الشفافية
        if bTransparent {
            # تفعيل الشفافية
        }
        
        # تطبيق خصائص أخرى حسب الحاجة

    func calculateLighting aWorldPos, aNormal, aViewDir, aLights
        # حساب الإضاءة للمادة
        aFinalColor = [0, 0, 0]
        
        # الإضاءة المحيطة
        nAmbientStrength = 0.1
        aAmbientColor = [oAlbedoColor.r / 255.0 * nAmbientStrength,
                         oAlbedoColor.g / 255.0 * nAmbientStrength,
                         oAlbedoColor.b / 255.0 * nAmbientStrength]
        
        aFinalColor[1] += aAmbientColor[1]
        aFinalColor[2] += aAmbientColor[2]
        aFinalColor[3] += aAmbientColor[3]
        
        # حساب إضاءة كل مصدر ضوء
        for oLight in aLights {
            if oLight.isEnabled() {
                aLightContrib = oLight.calculateLightContribution(aWorldPos, aNormal)
                
                # إضافة الإضاءة المنتشرة
                aFinalColor[1] += aLightContrib[1] * oAlbedoColor.r / 255.0
                aFinalColor[2] += aLightContrib[2] * oAlbedoColor.g / 255.0
                aFinalColor[3] += aLightContrib[3] * oAlbedoColor.b / 255.0
                
                # حساب الإضاءة اللامعة (Specular)
                if nSpecularPower > 0 {
                    aSpecularContrib = calculateSpecular(aWorldPos, aNormal, aViewDir, oLight)
                    aFinalColor[1] += aSpecularContrib[1]
                    aFinalColor[2] += aSpecularContrib[2]
                    aFinalColor[3] += aSpecularContrib[3]
                }
            }
        }
        
        # إضافة الإضاءة الذاتية
        if nEmissiveStrength > 0 {
            aFinalColor[1] += oEmissiveColor.r / 255.0 * nEmissiveStrength
            aFinalColor[2] += oEmissiveColor.g / 255.0 * nEmissiveStrength
            aFinalColor[3] += oEmissiveColor.b / 255.0 * nEmissiveStrength
        }
        
        # تحديد اللون النهائي
        aFinalColor[1] = clamp(aFinalColor[1], 0.0, 1.0)
        aFinalColor[2] = clamp(aFinalColor[2], 0.0, 1.0)
        aFinalColor[3] = clamp(aFinalColor[3], 0.0, 1.0)
        
        return aFinalColor

    func calculateSpecular aWorldPos, aNormal, aViewDir, oLight
        # حساب الإضاءة اللامعة
        aLightPos = oLight.getPosition()
        aLightDir = [aLightPos[1] - aWorldPos[1],
                     aLightPos[2] - aWorldPos[2],
                     aLightPos[3] - aWorldPos[3]]
        
        # تطبيع الاتجاه
        nLength = sqrt(aLightDir[1]*aLightDir[1] + 
                      aLightDir[2]*aLightDir[2] + 
                      aLightDir[3]*aLightDir[3])
        if nLength > 0 {
            aLightDir[1] /= nLength
            aLightDir[2] /= nLength
            aLightDir[3] /= nLength
        }
        
        # حساب اتجاه الانعكاس
        nDotProduct = 2.0 * (aNormal[1]*aLightDir[1] + 
                            aNormal[2]*aLightDir[2] + 
                            aNormal[3]*aLightDir[3])
        
        aReflectDir = [nDotProduct*aNormal[1] - aLightDir[1],
                       nDotProduct*aNormal[2] - aLightDir[2],
                       nDotProduct*aNormal[3] - aLightDir[3]]
        
        # حساب قوة الانعكاس
        nSpecDot = max(aReflectDir[1]*aViewDir[1] + 
                      aReflectDir[2]*aViewDir[2] + 
                      aReflectDir[3]*aViewDir[3], 0.0)
        
        nSpecStrength = pow(nSpecDot, nSpecularPower)
        
        oLightColor = oLight.getColor()
        return [oSpecularColor.r / 255.0 * nSpecStrength * oLightColor.r / 255.0,
                oSpecularColor.g / 255.0 * nSpecStrength * oLightColor.g / 255.0,
                oSpecularColor.b / 255.0 * nSpecStrength * oLightColor.b / 255.0]

    func clamp nValue, nMin, nMax
        if nValue < nMin {
            return nMin
        }
        if nValue > nMax {
            return nMax
        }
        return nValue

    func max nA, nB
        if nA > nB {
            return nA
        }
        return nB

    func pow nBase, nExp
        # تنفيذ مبسط للأس
        if nExp = 0 {
            return 1
        }
        nResult = nBase
        for i = 2 to nExp {
            nResult *= nBase
        }
        return nResult

    func getName
        return cName

    func setName cNewName
        cName = cNewName

    func getID
        return cID
    
    private
        cName = "DefaultMaterial"
        cID 
        oAlbedoColor
        oEmissiveColor
        oSpecularColor
        oAlbedoTexture
        oNormalTexture
        oSpecularTexture
        oEmissiveTexture
        oRoughnessTexture
        oMetallicTexture
        oAOTexture
        nRoughness
        nMetallic
        nSpecularPower
        nEmissiveStrength
        nAlpha
        nRefractionIndex
        nTextureScaleU
        nTextureScaleV
        nTextureOffsetU
        nTextureOffsetV
        bTransparent
        nBlendMode
        bDoubleSided
        bCastShadows
        bReceiveShadows
}
