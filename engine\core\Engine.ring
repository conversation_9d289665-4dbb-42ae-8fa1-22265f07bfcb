/*
الكلاس: Engine
الوصف: المحرك الرئيسي الذي يدير جميع أنظمة اللعبة
المدخلات: إعدادات التهيئة
المخرجات: محرك ألعاب متكامل وقابل للتشغيل
*/

/* load "raylib.ring"
load "WindowManager.ring"
load "Renderer.ring"
load "ResourceManager.ring"
load "GameObject.ring"
load "Component.ring"
load "Resource.ring"
load "../scene/SceneManager.ring"
load "../scene/Scene.ring"
load "../scene/SceneGraph.ring"
load "../input/InputManager.ring"
load "../input/InputMapping.ring"
load "../physics/PhysicsEngine.ring"
load "../audio/AudioEngine.ring" */

class Engine {

    func init nWidth, nHeight, cTitle    # nWidth = 1280, nHeight = 720, cTitle = "محرك الألعاب الاحترافي"
        # تهيئة النافذة أولاً
        InitWindow(nWidth, nHeight, cTitle)
        SetTargetFPS(60)

        # تهيئة الأنظمة
        oWindow = new WindowManager()
        oRenderer = new Renderer()
        oSceneManager = new SceneManager()
        oResourceManager = new ResourceManager()
        oInputManager = new InputManager()
        oPhysicsEngine = new PhysicsEngine()
        oAudioEngine = new AudioEngine()

        # متغيرات التحكم
        bIsRunning = true
        bInitialized = true
        nFrameCount = 0
        nLastFPSTime = GetTime()
        nCurrentFPS = 0

        # إعداد المحرك
        setupEngine()

    func setupEngine
        # إعداد الكاميرا الافتراضية
        oCamera = oWindow.getCamera()

        # إنشاء مشهد افتراضي
        oDefaultScene = new Scene("DefaultScene")
        oSceneManager.addScene(oDefaultScene)
        oSceneManager.setCurrentScene(oDefaultScene)

        # تسجيل مستمعي الأحداث
        oInputManager.addEventListener(this)

    func start
        if not bInitialized {
            ? "خطأ: المحرك غير مهيأ بشكل صحيح"
            return false
        }

        ? "بدء تشغيل المحرك..."

        # الحلقة الرئيسية
        while bIsRunning and not WindowShouldClose() {
            nDeltaTime = GetFrameTime()

            update(nDeltaTime)
            render()

            nFrameCount++
            updateFPS()
        }

        cleanup()
        return true

    func update nDeltaTime
        # تحديث المدخلات
        oInputManager.update()

        # تحديث الفيزياء
        oPhysicsEngine.update()

        # تحديث المشاهد
        oSceneManager.update(nDeltaTime)

        # تحديث الصوت
        oAudioEngine.update()

        # معالجة الأحداث
        processEvents()

    func render
        # بداية الرسم
        oRenderer.beginFrame()

        # رسم المشهد الحالي
        oSceneManager.render()

        # رسم واجهة المستخدم
        renderUI()

        # انتهاء الرسم
        oRenderer.endFrame()

    func renderUI
        # رسم معلومات التصحيح
        if bShowDebugInfo {
            DrawText("FPS: " + string(nCurrentFPS), 10, 10, 20, GREEN)
            DrawText("Objects: " + string(getObjectCount()), 10, 35, 20, GREEN)
            DrawText("Triangles: " + string(getTriangleCount()), 10, 60, 20, GREEN)
        }

    func processEvents
        # معالجة أحداث خاصة بالمحرك
        if oInputManager.isKeyPressed(KEY_F1) {
            toggleDebugInfo()
        }

        if oInputManager.isKeyPressed(KEY_F11) {
            oWindow.toggleFullscreen()
        }

        if oInputManager.isKeyPressed(KEY_ESCAPE) {
            bIsRunning = false
        }

    func updateFPS
        nCurrentTime = GetTime()
        if nCurrentTime - nLastFPSTime >= 1.0 {
            nCurrentFPS = nFrameCount
            nFrameCount = 0
            nLastFPSTime = nCurrentTime
        }

    func addGameObject oObject, cSceneName    # = ""
        if cSceneName = "" {
            oCurrentScene = oSceneManager.getCurrentScene()
            if oCurrentScene != null {
                return oCurrentScene.addGameObject(oObject)
            }
        else
            oScene = oSceneManager.getScene(cSceneName)
            if oScene != null {
                return oScene.addGameObject(oObject)
            }
        }
        return false

    func removeGameObject oObject, cSceneName    # = ""
        if cSceneName = "" {
            oCurrentScene = oSceneManager.getCurrentScene()
            if oCurrentScene != null {
                return oCurrentScene.removeGameObject(oObject)
            }
        else
            oScene = oSceneManager.getScene(cSceneName)
            if oScene != null {
                return oScene.removeGameObject(oObject)
            }
        }
        return false

    func loadModel cPath, cName    # = ""
        return oResourceManager.loadModel(cPath, cName)

    func loadTexture cPath, cName    # = ""
        return oResourceManager.loadTexture(cPath, cName)

    func loadSound cPath, cName    # = ""
        return oResourceManager.loadSound(cPath, cName)

    func loadMusic cPath, cName    # = ""
        return oResourceManager.loadMusic(cPath, cName)

    func getObjectCount
        oCurrentScene = oSceneManager.getCurrentScene()
        if oCurrentScene != null {
            return oCurrentScene.getObjectCount()
        }
        return 0

    func getTriangleCount
        oCurrentScene = oSceneManager.getCurrentScene()
        if oCurrentScene != null {
            return oCurrentScene.getTriangleCount()
        }
        return 0

    func toggleDebugInfo
        bShowDebugInfo = not bShowDebugInfo

    func isRunning
        return bIsRunning

    func stop
        bIsRunning = false

    func onInputEvent cAction
        # معالجة أحداث المدخلات
        switch cAction
        on "Menu"
            bIsRunning = false
        off

    func cleanup
        ? "تنظيف موارد المحرك..."

        # تنظيف الأنظمة
        oSceneManager.cleanup()
        oResourceManager.cleanup()
        oAudioEngine.cleanup()
        oPhysicsEngine.cleanup()

        # إغلاق النافذة
        CloseWindow()

        ? "تم إغلاق المحرك بنجاح"

    func getWindow
        return oWindow

    func getRenderer
        return oRenderer

    func getSceneManager
        return oSceneManager

    func getResourceManager
        return oResourceManager

    func getInputManager
        return oInputManager

    func getPhysicsEngine
        return oPhysicsEngine

    func getAudioEngine
        return oAudioEngine

    private
        oWindow
        oRenderer
        oSceneManager
        oResourceManager
        oInputManager
        oPhysicsEngine
        oAudioEngine
        bIsRunning
        bInitialized
        bShowDebugInfo = false
        nFrameCount
        nLastFPSTime
        nCurrentFPS
}
