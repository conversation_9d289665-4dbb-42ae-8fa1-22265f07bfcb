/*
الكلاس: GameCamera
الوصف: نظام كاميرا متقدم مع دعم أنواع مختلفة من الكاميرات
المدخلات: موقع الكاميرا، الهدف، الإعدادات
المخرجات: كاميرا قابلة للتحكم والتحريك
*/

//load "raylib.ring"

# أنواع الكاميرا
CAMERA_TYPE_PERSPECTIVE = 0
CAMERA_TYPE_ORTHOGRAPHIC = 1

# أنماط التحكم بالكاميرا
CAMERA_CONTROL_FREE = 0
CAMERA_CONTROL_ORBITAL = 1
CAMERA_CONTROL_FIRST_PERSON = 2
CAMERA_CONTROL_THIRD_PERSON = 3

class GameCamera {
    
    func init aPos, aTarget, aUpVec    #aPos = [0, 10, 10], aTarget = [0, 0, 0], aUpVec = [0, 1, 0]
        # إعداد الكاميرا الأساسي
        aPosition = aPos
        aTargetPosition = aTarget
        aUpVector = aUpVec
        
        # خصائص الكاميرا
        nFieldOfView = 45.0
        nNearPlane = 0.1
        nFarPlane = 1000.0
        nAspectRatio = 16.0 / 9.0
        
        # نوع الكاميرا
        nCameraType = CAMERA_TYPE_PERSPECTIVE
        nControlMode = CAMERA_CONTROL_FREE
        
        # خصائص الكاميرا المتعامدة
        nOrthographicSize = 10.0
        
        # خصائص التحكم
        nMovementSpeed = 5.0
        nRotationSpeed = 2.0
        nZoomSpeed = 1.0
        nMouseSensitivity = 0.003
        
        # حالة التحكم
        bMouseLocked = false
        aLastMousePosition = [0, 0]
        
        # خصائص الكاميرا المدارية
        nOrbitDistance = 10.0
        nOrbitAngleH = 0.0  # الزاوية الأفقية
        nOrbitAngleV = 0.0  # الزاوية العمودية
        nMinOrbitDistance = 1.0
        nMaxOrbitDistance = 100.0
        
        # خصائص الشخص الأول/الثالث
        nPitch = 0.0
        nYaw = 0.0
        nRoll = 0.0
        
        # كاميرا RayLib
        oRayLibCamera = new Camera3D(
            aPosition[1], aPosition[2], aPosition[3],
            aTargetPosition[1], aTargetPosition[2], aTargetPosition[3],
            aUpVector[1], aUpVector[2], aUpVector[3],
            nFieldOfView,
            CAMERA_PERSPECTIVE
        )
        
        # معرف الكاميرا
        cID = generateCameraID()
        
        updateCameraVectors()

    func generateCameraID
        return "CAM_" + clock() + "_" + random(9999)

    func update nDeltaTime, oInputManager
        # تحديث الكاميرا حسب نمط التحكم
        switch nControlMode
        on CAMERA_CONTROL_FREE
            updateFreeCamera(nDeltaTime, oInputManager)
        on CAMERA_CONTROL_ORBITAL
            updateOrbitalCamera(nDeltaTime, oInputManager)
        on CAMERA_CONTROL_FIRST_PERSON
            updateFirstPersonCamera(nDeltaTime, oInputManager)
        on CAMERA_CONTROL_THIRD_PERSON
            updateThirdPersonCamera(nDeltaTime, oInputManager)
        off
        
        # تحديث كاميرا RayLib
        updateRayLibCamera()

    func updateFreeCamera nDeltaTime, oInputManager
        # كاميرا حرة الحركة
        nSpeed = nMovementSpeed * nDeltaTime
        
        # حركة الكاميرا
        if oInputManager.isKeyDown(KEY_W) {
            moveForward(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_S) {
            moveBackward(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_A) {
            moveLeft(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_D) {
            moveRight(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_Q) {
            moveUp(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_E) {
            moveDown(nSpeed)
        }
        
        # دوران الكاميرا بالفأرة
        if oInputManager.isMouseButtonDown(MOUSE_RIGHT_BUTTON) {
            aMouseDelta = oInputManager.getMouseDelta()
            nYaw -= aMouseDelta[1] * nMouseSensitivity
            nPitch -= aMouseDelta[2] * nMouseSensitivity
            
            # تحديد زاوية الميل
            if nPitch > 89.0 { nPitch = 89.0 }
            if nPitch < -89.0 { nPitch = -89.0 }
            
            updateCameraVectors()
        }

    func updateOrbitalCamera nDeltaTime, oInputManager
        # كاميرا مدارية حول هدف
        
        # تغيير المسافة بالعجلة
        nWheelMove = oInputManager.getMouseWheelMove()
        if nWheelMove != 0 {
            nOrbitDistance -= nWheelMove * nZoomSpeed
            if nOrbitDistance < nMinOrbitDistance { nOrbitDistance = nMinOrbitDistance }
            if nOrbitDistance > nMaxOrbitDistance { nOrbitDistance = nMaxOrbitDistance }
        }
        
        # دوران حول الهدف
        if oInputManager.isMouseButtonDown(MOUSE_LEFT_BUTTON) {
            aMouseDelta = oInputManager.getMouseDelta()
            nOrbitAngleH += aMouseDelta[1] * nMouseSensitivity
            nOrbitAngleV += aMouseDelta[2] * nMouseSensitivity
            
            # تحديد الزاوية العمودية
            if nOrbitAngleV > 89.0 { nOrbitAngleV = 89.0 }
            if nOrbitAngleV < -89.0 { nOrbitAngleV = -89.0 }
        }
        
        # حساب موقع الكاميرا
        nRadH = nOrbitAngleH * 3.14159 / 180.0
        nRadV = nOrbitAngleV * 3.14159 / 180.0
        
        aPosition[1] = aTargetPosition[1] + nOrbitDistance * cos(nRadV) * cos(nRadH)
        aPosition[2] = aTargetPosition[2] + nOrbitDistance * sin(nRadV)
        aPosition[3] = aTargetPosition[3] + nOrbitDistance * cos(nRadV) * sin(nRadH)

    func updateFirstPersonCamera nDeltaTime, oInputManager
        # كاميرا الشخص الأول
        nSpeed = nMovementSpeed * nDeltaTime
        
        # حركة الكاميرا
        if oInputManager.isKeyDown(KEY_W) {
            moveForward(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_S) {
            moveBackward(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_A) {
            moveLeft(nSpeed)
        }
        if oInputManager.isKeyDown(KEY_D) {
            moveRight(nSpeed)
        }
        
        # دوران بالفأرة
        aMouseDelta = oInputManager.getMouseDelta()
        nYaw += aMouseDelta[1] * nMouseSensitivity
        nPitch -= aMouseDelta[2] * nMouseSensitivity
        
        # تحديد زاوية الميل
        if nPitch > 89.0 { nPitch = 89.0 }
        if nPitch < -89.0 { nPitch = -89.0 }
        
        updateCameraVectors()

    func updateThirdPersonCamera nDeltaTime, oInputManager
        # كاميرا الشخص الثالث (تتبع هدف)
        # مشابهة للكاميرا المدارية لكن مع إزاحة ثابتة
        updateOrbitalCamera(nDeltaTime, oInputManager)

    func updateCameraVectors
        # حساب اتجاهات الكاميرا
        nRadYaw = nYaw * 3.14159 / 180.0
        nRadPitch = nPitch * 3.14159 / 180.0
        
        # اتجاه الأمام
        aForward = [
            cos(nRadYaw) * cos(nRadPitch),
            sin(nRadPitch),
            sin(nRadYaw) * cos(nRadPitch)
        ]
        
        # تطبيع الاتجاه
        aForward = normalizeVector(aForward)
        
        # حساب الهدف
        aTargetPosition[1] = aPosition[1] + aForward[1]
        aTargetPosition[2] = aPosition[2] + aForward[2]
        aTargetPosition[3] = aPosition[3] + aForward[3]
        
        # حساب اتجاه اليمين
        aRight = crossProduct(aForward, [0, 1, 0])
        aRight = normalizeVector(aRight)
        
        # حساب اتجاه الأعلى
        aUpVector = crossProduct(aRight, aForward)
        aUpVector = normalizeVector(aUpVector)

    func updateRayLibCamera
        # تحديث كاميرا RayLib
        oRayLibCamera.position = Vector3(aPosition[1], aPosition[2], aPosition[3])
        oRayLibCamera.target = Vector3(aTargetPosition[1], aTargetPosition[2], aTargetPosition[3])
        oRayLibCamera.up = Vector3(aUpVector[1], aUpVector[2], aUpVector[3])
        oRayLibCamera.fovy = nFieldOfView
        
        if nCameraType = CAMERA_TYPE_PERSPECTIVE {
            oRayLibCamera.projection = CAMERA_PERSPECTIVE
        else
            oRayLibCamera.projection = CAMERA_ORTHOGRAPHIC
        }

    func moveForward nDistance
        aForward = [aTargetPosition[1] - aPosition[1],
                    aTargetPosition[2] - aPosition[2],
                    aTargetPosition[3] - aPosition[3]]
        aForward = normalizeVector(aForward)
        
        aPosition[1] += aForward[1] * nDistance
        aPosition[2] += aForward[2] * nDistance
        aPosition[3] += aForward[3] * nDistance

    func moveBackward nDistance
        moveForward(-nDistance)

    func moveLeft nDistance
        aForward = [aTargetPosition[1] - aPosition[1],
                    aTargetPosition[2] - aPosition[2],
                    aTargetPosition[3] - aPosition[3]]
        aRight = crossProduct(aForward, aUpVector)
        aRight = normalizeVector(aRight)
        
        aPosition[1] -= aRight[1] * nDistance
        aPosition[2] -= aRight[2] * nDistance
        aPosition[3] -= aRight[3] * nDistance

    func moveRight nDistance
        moveLeft(-nDistance)

    func moveUp nDistance
        aPosition[2] += nDistance

    func moveDown nDistance
        aPosition[2] -= nDistance

    func lookAt aTarget
        aTargetPosition = aTarget
        updateCameraVectors()

    func setPosition aPos
        aPosition = aPos

    func getPosition
        return aPosition

    func setTarget aTarget
        aTargetPosition = aTarget

    func getTarget
        return aTargetPosition

    func setFieldOfView nFOV
        nFieldOfView = nFOV
        if nFieldOfView < 1.0 { nFieldOfView = 1.0 }
        if nFieldOfView > 179.0 { nFieldOfView = 179.0 }

    func getFieldOfView
        return nFieldOfView

    func setControlMode nMode
        nControlMode = nMode

    func getControlMode
        return nControlMode

    func setCameraType nType
        nCameraType = nType

    func getCameraType
        return nCameraType

    func setMovementSpeed nSpeed
        nMovementSpeed = nSpeed

    func getMovementSpeed
        return nMovementSpeed

    func setMouseSensitivity nSens
        nMouseSensitivity = nSens

    func getMouseSensitivity
        return nMouseSensitivity

    func getRayLibCamera
        return oRayLibCamera

    func getID
        return cID

    func normalizeVector aVec
        nLength = sqrt(aVec[1]*aVec[1] + aVec[2]*aVec[2] + aVec[3]*aVec[3])
        if nLength > 0 {
            return [aVec[1]/nLength, aVec[2]/nLength, aVec[3]/nLength]
        }
        return [0, 0, 0]

    func crossProduct aVec1, aVec2
        return [
            aVec1[2] * aVec2[3] - aVec1[3] * aVec2[2],
            aVec1[3] * aVec2[1] - aVec1[1] * aVec2[3],
            aVec1[1] * aVec2[2] - aVec1[2] * aVec2[1]
        ]

    private
        cID
        aPosition
        aTargetPosition
        aUpVector
        nFieldOfView
        nNearPlane
        nFarPlane
        nAspectRatio
        nCameraType
        nControlMode
        nOrthographicSize
        nMovementSpeed
        nRotationSpeed
        nZoomSpeed
        nMouseSensitivity
        bMouseLocked
        aLastMousePosition
        nOrbitDistance
        nOrbitAngleH
        nOrbitAngleV
        nMinOrbitDistance
        nMaxOrbitDistance
        nPitch
        nYaw
        nRoll
        oRayLibCamera
}
