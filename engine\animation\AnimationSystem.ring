

class AnimationSystem {
    

    func init
        initializeAnimationSystem()

    func initializeAnimationSystem
        # تهيئة نظام التحريك

    func createAnimation cName
        oAnimation = new Animation {
            cName = cName
            aTracks = []
            nDuration = 0
            nCurrentTime = 0
            bLooping = true
            nPlaybackSpeed = 1.0
            bIsPlaying = false
        }
        add(aAnimations, oAnimation)
        return oAnimation

    func createSkeleton
        oSkeleton = new Skeleton {
            aBones = []
            aBindPose = []
        }
        add(aSkeletons, oSkeleton)
        return oSkeleton

    func createBlendTree
        oBlendTree = new AnimationBlendTree
        add(aBlendTrees, oBlendTree)
        return oBlendTree

    func update
        nDeltaTime = GetFrameTime()
        
        # تحديث كل التحريكات النشطة
        for oAnimation in aAnimations {
            if oAnimation.bIsPlaying {
                updateAnimation(oAnimation)
            }
        }

        # تحديث أشجار المزج
        for oBlendTree in aBlendTrees {
            oBlendTree.update(nDeltaTime)
        }

    func updateAnimation oAnimation
        if not oAnimation.bIsPlaying {
            return
        }

        # تحديث وقت التحريك
        oAnimation.nCurrentTime += nDeltaTime * oAnimation.nPlaybackSpeed

        # التعامل مع التكرار
        if oAnimation.nCurrentTime >= oAnimation.nDuration {
            if oAnimation.bLooping {
                oAnimation.nCurrentTime = 0
            else
                oAnimation.bIsPlaying = false
                oAnimation.nCurrentTime = oAnimation.nDuration
            }
        }

        # تحديث كل المسارات
        for oTrack in oAnimation.aTracks {
            updateTrack(oTrack, oAnimation.nCurrentTime)
        }

    func updateTrack oTrack, nTime
        # العثور على الإطارات المناسبة للوقت الحالي
        aFrames = findKeyframes(oTrack, nTime)
        
        if len(aFrames) >= 2 {
            # حساب قيمة المزج بين الإطارين
            nBlendFactor = calculateBlendFactor(aFrames[1], aFrames[2], nTime)
            
            # مزج القيم
            interpolateKeyframes(aFrames[1], aFrames[2], nBlendFactor)
        }

    func playAnimation oAnimation, nBlendTime  # = 0.3
        if not oAnimation {
            return
        }

        if nBlendTime > 0 {
            # مزج مع التحريك الحالي
            blendToAnimation(oAnimation, nBlendTime)
        else
            # تشغيل مباشر
            oAnimation.bIsPlaying = true
            oAnimation.nCurrentTime = 0
        }

    func stopAnimation oAnimation
        if oAnimation {
            oAnimation.bIsPlaying = false
            oAnimation.nCurrentTime = 0
        }

    func pauseAnimation oAnimation
        if oAnimation {
            oAnimation.bIsPlaying = false
        }

    func resumeAnimation oAnimation
        if oAnimation {
            oAnimation.bIsPlaying = true
        }

    func setAnimationSpeed oAnimation, nSpeed
        if oAnimation {
            oAnimation.nPlaybackSpeed = nSpeed
        }

    func blendToAnimation oTargetAnim, nBlendTime
        # إنشاء انتقال متدرج بين التحريكات
        oBlend = new AnimationBlend {
            oSourceAnim = getCurrentAnimation()
            oTargetAnim = oTargetAnim
            nDuration = nBlendTime
            nCurrentTime = 0
        }
        
        startBlend(oBlend)

    private
        aAnimations = []
        aSkeletons = []
        aBlendTrees = []
        nDeltaTime = 0

    func findKeyframes oTrack, nTime
        # البحث عن الإطارات المناسبة للوقت المحدد
        aResult = []
        
        for i = 1 to len(oTrack.aKeyframes)-1 {
            if oTrack.aKeyframes[i].nTime <= nTime and 
               oTrack.aKeyframes[i+1].nTime >= nTime {
                add(aResult, oTrack.aKeyframes[i])
                add(aResult, oTrack.aKeyframes[i+1])
                exit
            }
        }
        
        return aResult

    func calculateBlendFactor oFrame1, oFrame2, nCurrentTime
        # حساب معامل المزج بين إطارين
        return (nCurrentTime - oFrame1.nTime) / (oFrame2.nTime - oFrame1.nTime)

    func interpolateKeyframes oFrame1, oFrame2, nBlendFactor
        # مزج القيم بين إطارين
        if oFrame1.cType = "transform" {
            return interpolateTransform(oFrame1.value, oFrame2.value, nBlendFactor)
        }
        return lerp(oFrame1.value, oFrame2.value, nBlendFactor)

    func interpolateTransform transform1, transform2, nBlendFactor
        # مزج التحويلات (الموقع، الدوران، المقياس)
        return new Transform {
            position = lerpVector(transform1.position, transform2.position, nBlendFactor)
            rotation = slerpQuaternion(transform1.rotation, transform2.rotation, nBlendFactor)
            scale = lerpVector(transform1.scale, transform2.scale, nBlendFactor)
        }

    func lerp x, y, a
        return x * (1-a) + y * a

    func lerpVector v1, v2, a
        return [
            lerp(v1[1], v2[1], a),
            lerp(v1[2], v2[2], a),
            lerp(v1[3], v2[3], a)
        ]

    func cleanup
        # تنظيف موارد نظام التحريك
}
