/*
الكلاس: BoxCollider
الوصف: كاشف تصادم مكعب الشكل
المدخلات: حجم وموقع المكعب
المخرجات: كشف التصادمات مع الكائنات الأخرى
*/

//load "../core/Component.ring"

class BoxCollider from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("BoxCollider")
        
        # خصائص المكعب
        aSize = [1.0, 1.0, 1.0]
        aCenter = [0.0, 0.0, 0.0]
        
        # خصائص الفيزياء
        bIsTrigger = false
        bIsStatic = false
        nMass = 1.0
        nRestitution = 0.5  # الارتداد
        nFriction = 0.7     # الاحتكاك
        
        # حالة التصادم
        bIsColliding = false
        aCollidingObjects = []
        
        # مادة الفيزياء
        oPhysicsMaterial = null

    func setSize aNewSize
        aSize = aNewSize
        updateBounds()

    func getSize
        return aSize

    func setCenter aNewCenter
        aCenter = aNewCenter
        updateBounds()

    func getCenter
        return aCenter

    func setTrigger bState
        bIsTrigger = bState

    func isTrigger
        return bIsTrigger

    func setStatic bState
        bIsStatic = bState

    func isStatic
        return bIsStatic

    func setMass nNewMass
        nMass = nNewMass

    func getMass
        return nMass

    func setRestitution nValue
        nRestitution = nValue

    func getRestitution
        return nRestitution

    func setFriction nValue
        nFriction = nValue

    func getFriction
        return nFriction

    func setPhysicsMaterial oMaterial
        oPhysicsMaterial = oMaterial

    func getPhysicsMaterial
        return oPhysicsMaterial

    func updateBounds
        # تحديث حدود المكعب
        if oGameObject != null {
            aWorldPos = oGameObject.getPosition()
            aWorldScale = oGameObject.getScale()
            
            # حساب الحدود في الإحداثيات العالمية
            nHalfX = (aSize[1] * aWorldScale[1]) / 2.0
            nHalfY = (aSize[2] * aWorldScale[2]) / 2.0
            nHalfZ = (aSize[3] * aWorldScale[3]) / 2.0
            
            aBoundsMin = [
                aWorldPos[1] + aCenter[1] - nHalfX,
                aWorldPos[2] + aCenter[2] - nHalfY,
                aWorldPos[3] + aCenter[3] - nHalfZ
            ]
            
            aBoundsMax = [
                aWorldPos[1] + aCenter[1] + nHalfX,
                aWorldPos[2] + aCenter[2] + nHalfY,
                aWorldPos[3] + aCenter[3] + nHalfZ
            ]
        }

    func getBounds
        return [aBoundsMin, aBoundsMax]

    func getBoundsMin
        return aBoundsMin

    func getBoundsMax
        return aBoundsMax

    func checkCollision oOtherCollider
        # فحص التصادم مع كاشف آخر
        if oOtherCollider = null {
            return false
        }
        
        # تحديث الحدود
        updateBounds()
        oOtherCollider.updateBounds()
        
        # فحص تداخل AABB (Axis-Aligned Bounding Box)
        aOtherMin = oOtherCollider.getBoundsMin()
        aOtherMax = oOtherCollider.getBoundsMax()
        
        # فحص التداخل في كل محور
        bOverlapX = aBoundsMax[1] >= aOtherMin[1] and aBoundsMin[1] <= aOtherMax[1]
        bOverlapY = aBoundsMax[2] >= aOtherMin[2] and aBoundsMin[2] <= aOtherMax[2]
        bOverlapZ = aBoundsMax[3] >= aOtherMin[3] and aBoundsMin[3] <= aOtherMax[3]
        
        return bOverlapX and bOverlapY and bOverlapZ

    func getContactPoints oOtherCollider
        # حساب نقاط التلامس
        aContactPoints = []
        
        if checkCollision(oOtherCollider) {
            # حساب نقطة التلامس المتوسطة
            aOtherMin = oOtherCollider.getBoundsMin()
            aOtherMax = oOtherCollider.getBoundsMax()
            
            nContactX = (max(aBoundsMin[1], aOtherMin[1]) + min(aBoundsMax[1], aOtherMax[1])) / 2.0
            nContactY = (max(aBoundsMin[2], aOtherMin[2]) + min(aBoundsMax[2], aOtherMax[2])) / 2.0
            nContactZ = (max(aBoundsMin[3], aOtherMin[3]) + min(aBoundsMax[3], aOtherMax[3])) / 2.0
            
            add(aContactPoints, [nContactX, nContactY, nContactZ])
        }
        
        return aContactPoints

    func getCollisionNormal oOtherCollider
        # حساب اتجاه التصادم
        if not checkCollision(oOtherCollider) {
            return [0, 0, 0]
        }
        
        # حساب المسافات للتداخل في كل محور
        aOtherMin = oOtherCollider.getBoundsMin()
        aOtherMax = oOtherCollider.getBoundsMax()
        
        nOverlapX = min(aBoundsMax[1] - aOtherMin[1], aOtherMax[1] - aBoundsMin[1])
        nOverlapY = min(aBoundsMax[2] - aOtherMin[2], aOtherMax[2] - aBoundsMin[2])
        nOverlapZ = min(aBoundsMax[3] - aOtherMin[3], aOtherMax[3] - aBoundsMin[3])
        
        # العثور على أصغر تداخل (محور الفصل)
        if nOverlapX < nOverlapY and nOverlapX < nOverlapZ {
            # التصادم في المحور X
            if aBoundsMin[1] < aOtherMin[1] {
                return [-1, 0, 0]
            else
                return [1, 0, 0]
            }
        elseif nOverlapY < nOverlapZ
            # التصادم في المحور Y
            if aBoundsMin[2] < aOtherMin[2] {
                return [0, -1, 0]
            else
                return [0, 1, 0]
            }
        else
            # التصادم في المحور Z
            if aBoundsMin[3] < aOtherMin[3] {
                return [0, 0, -1]
            else
                return [0, 0, 1]
            }
        }

    func onCollisionEnter oOtherCollider
        # استدعى عند بداية التصادم
        if not find(aCollidingObjects, oOtherCollider) {
            add(aCollidingObjects, oOtherCollider)
            bIsColliding = true
            
            # إشعار الكائن المالك
            if oGameObject != null {
                oGameObject.onCollisionEnter(this, oOtherCollider)
            }
        }

    func onCollisionExit oOtherCollider
        # استدعى عند انتهاء التصادم
        nIndex = find(aCollidingObjects, oOtherCollider)
        if nIndex > 0 {
            del(aCollidingObjects, nIndex)
            
            if len(aCollidingObjects) = 0 {
                bIsColliding = false
            }
            
            # إشعار الكائن المالك
            if oGameObject != null {
                oGameObject.onCollisionExit(this, oOtherCollider)
            }
        }

    func update nDeltaTime
        # تحديث المكون
        updateBounds()

    func isColliding
        return bIsColliding

    func getCollidingObjects
        return aCollidingObjects

    func getShape
        return "box"

    private
        aSize
        aCenter
        bIsTrigger
        bIsStatic
        nMass
        nRestitution
        nFriction
        bIsColliding
        aCollidingObjects
        oPhysicsMaterial
        aBoundsMin
        aBoundsMax
}
