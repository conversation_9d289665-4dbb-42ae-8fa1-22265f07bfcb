//load "raylib.ring"

class AudioEngine {
    
    func init
        InitAudioDevice()
        SetMasterVolume(nMasterVolume)

    func loadSound cPath
        oSound = LoadSound(cPath)
        add(aSounds, new AudioResource(oSound, cPath))
        return oSound

    func loadMusic cPath
        oMusic = LoadMusicStream(cPath)
        add(aMusic, new AudioResource(oMusic, cPath))
        return oMusic

    func playSound oSound, nVolume , nPitch
        SetSoundVolume(oSound, nVolume * nSoundVolume)
        SetSoundPitch(oSound, nPitch)
        PlaySound(oSound)

    func playMusic oMusic, bLoop
        SetMusicVolume(oMusic, nMusicVolume)
        PlayMusicStream(oMusic)
        if bLoop {
            oMusic.looping = true
        }

    func stopSound oSound
        StopSound(oSound)

    func stopMusic oMusic
        StopMusicStream(oMusic)

    func pauseSound oSound
        PauseSound(oSound)

    func pauseMusic oMusic
        PauseMusicStream(oMusic)

    func resumeSound oSound
        ResumeSound(oSound)

    func resumeMusic oMusic
        ResumeMusicStream(oMusic)

    func update
        # تحديث جميع مقاطع الموسيقى النشطة
        for oMusic in aMusic {
            if IsMusicStreamPlaying(oMusic.resource) {
                UpdateMusicStream(oMusic.resource)
            }
        }

    func setMasterVolume nVolume
        nMasterVolume = nVolume
        //SetMasterVolume(nMasterVolume)

    func setSoundVolume nVolume
        nSoundVolume = nVolume
        # تحديث مستوى صوت جميع المؤثرات الصوتية
        for oSound in aSounds {
            SetSoundVolume(oSound.resource, nVolume)
        }

    func setMusicVolume nVolume
        nMusicVolume = nVolume
        # تحديث مستوى صوت جميع الموسيقى
        for oMusic in aMusic {
            SetMusicVolume(oMusic.resource, nVolume)
        }

    func create3DAudio cPath, aPosition
        oStream = LoadAudioStream(44100, 16, 1)
        SetAudioStreamVolume(oStream, 1.0)
        SetAudioStreamPitch(oStream, 1.0)
        add(aAudioStreams, new Audio3D(oStream, aPosition))
        return oStream

    func update3DAudio oCameraPos
        # تحديث الأصوات ثلاثية الأبعاد بناءً على موقع الكاميرا
        for oAudio in aAudioStreams {
            oAudio.updatePosition(oCameraPos)
        }

    func cleanup
        # تحرير الموارد الصوتية
        for oSound in aSounds {
            UnloadSound(oSound.resource)
        }
        for oMusic in aMusic {
            UnloadMusicStream(oMusic.resource)
        }
        for oStream in aAudioStreams {
            UnloadAudioStream(oStream.resource)
        }
        CloseAudioDevice()

    private
        aSounds = []
        aMusic = []
        aAudioStreams = []
        nMasterVolume = 1.0
        nMusicVolume = 1.0
        nSoundVolume = 1.0
}
