/*
الكلاس: Component
الوصف: كلاس أساسي لجميع مكونات كائنات اللعبة
المدخلات: نوع المكون
المخرجات: مكون قابل للإرفاق بكائنات اللعبة
*/

class Component {

    func init cComponentType 
        cType = cComponentType
        bActive = true
        bVisible = true
        oOwner = null

    func setOwner oGameObject
        oOwner = oGameObject

    func getOwner
        return oOwner

    func getType
        return cType

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func setVisible bState
        bVisible = bState

    func isVisible
        return bVisible

    func onAttach
        # يتم استدعاؤها عند إرفاق المكون بكائن
        
    func onDetach
        # يتم استدعاؤها عند إزالة المكون من كائن
        
    func update nDeltaTime
        # تحديث المكون في كل إطار
        
    func render
        # رسم المكون
        
    func onCollisionEnter oOther
        # يتم استدعاؤها عند بداية التصادم
        
    func onCollisionExit oOther
        # يتم استدعاؤها عند انتهاء التصادم
        
    func onTriggerEnter oOther
        # يتم استدعاؤها عند دخول منطقة التحفيز
        
    func onTriggerExit oOther
        # يتم استدعاؤها عند الخروج من منطقة التحفيز

    private
        cType = "Component"
        bActive
        bVisible
        oOwner
}
