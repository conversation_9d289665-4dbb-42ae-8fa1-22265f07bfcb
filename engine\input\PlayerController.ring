/*
الكلاس: PlayerController
الوصف: مكون للتحكم في حركة اللاعب
المدخلات: مدخلات لوحة المفاتيح والفأرة
المخرجات: حركة وتفاعل اللاعب
*/

//load "../core/Component.ring"

class PlayerController from Component {
    
    func init
        # استدعاء منشئ الكلاس الأساسي
        super.init("PlayerController")
        
        # إعدادات الحركة
        nMovementSpeed = 5.0
        nRunSpeed = 8.0
        nJumpHeight = 2.0
        nMouseSensitivity = 2.0
        
        # حالة الحركة
        bIsGrounded = true
        bIsRunning = false
        bIsJumping = false
        bCanMove = true
        
        # اتجاه الحركة
        aMovementDirection = [0, 0, 0]
        aLookDirection = [0, 0, -1]
        
        # إعدادات الكاميرا
        nCameraPitch = 0.0
        nCameraYaw = 0.0
        nMaxPitch = 89.0
        
        # مراجع المكونات
        oRigidBody = null
        oCollider = null
        
        # إعدادات المدخلات
        setupInputMapping()

    func setupInputMapping
        # إعداد مفاتيح التحكم
        cMoveForward = "W"
        cMoveBackward = "S"
        cMoveLeft = "A"
        cMoveRight = "D"
        cJump = "SPACE"
        cRun = "LEFT_SHIFT"
        cCrouch = "LEFT_CONTROL"

    func setMovementSpeed nSpeed
        nMovementSpeed = nSpeed

    func getMovementSpeed
        return nMovementSpeed

    func setRunSpeed nSpeed
        nRunSpeed = nSpeed

    func getRunSpeed
        return nRunSpeed

    func setJumpHeight nHeight
        nJumpHeight = nHeight

    func getJumpHeight
        return nJumpHeight

    func setMouseSensitivity nSensitivity
        nMouseSensitivity = nSensitivity

    func getMouseSensitivity
        return nMouseSensitivity

    func setCanMove bState
        bCanMove = bState

    func getCanMove
        return bCanMove

    func isGrounded
        return bIsGrounded

    func isRunning
        return bIsRunning

    func isJumping
        return bIsJumping

    func start
        # البحث عن المكونات المطلوبة
        if oGameObject != null {
            oRigidBody = oGameObject.getComponent("RigidBody")
            oCollider = oGameObject.getComponent("BoxCollider")
            if oCollider = null {
                oCollider = oGameObject.getComponent("SphereCollider")
            }
        }

    func update nDeltaTime
        if not bCanMove {
            return
        }
        
        # معالجة مدخلات الحركة
        handleMovementInput(nDeltaTime)
        
        # معالجة مدخلات الفأرة
        handleMouseInput(nDeltaTime)
        
        # فحص الأرضية
        checkGrounded()
        
        # تحديث الحركة
        updateMovement(nDeltaTime)

    func handleMovementInput nDeltaTime
        # إعادة تعيين اتجاه الحركة
        aMovementDirection = [0, 0, 0]
        
        # فحص مفاتيح الحركة
        if IsKeyDown(KEY_W) {
            aMovementDirection[3] -= 1  # للأمام
        }
        if IsKeyDown(KEY_S) {
            aMovementDirection[3] += 1  # للخلف
        }
        if IsKeyDown(KEY_A) {
            aMovementDirection[1] -= 1  # لليسار
        }
        if IsKeyDown(KEY_D) {
            aMovementDirection[1] += 1  # لليمين
        }
        
        # تطبيع اتجاه الحركة
        nMagnitude = sqrt(aMovementDirection[1]*aMovementDirection[1] + 
                         aMovementDirection[3]*aMovementDirection[3])
        if nMagnitude > 0 {
            aMovementDirection[1] = aMovementDirection[1] / nMagnitude
            aMovementDirection[3] = aMovementDirection[3] / nMagnitude
        }
        
        # فحص الجري
        bIsRunning = IsKeyDown(KEY_LEFT_SHIFT)
        
        # فحص القفز
        if IsKeyPressed(KEY_SPACE) and bIsGrounded {
            jump()
        }

    func handleMouseInput nDeltaTime
        # الحصول على حركة الفأرة
        aMouseDelta = GetMouseDelta()
        
        if aMouseDelta[1] != 0 or aMouseDelta[2] != 0 {
            # تحديث زوايا الكاميرا
            nCameraYaw += aMouseDelta[1] * nMouseSensitivity * nDeltaTime
            nCameraPitch -= aMouseDelta[2] * nMouseSensitivity * nDeltaTime
            
            # تقييد زاوية الميل
            if nCameraPitch > nMaxPitch {
                nCameraPitch = nMaxPitch
            }
            if nCameraPitch < -nMaxPitch {
                nCameraPitch = -nMaxPitch
            }
            
            # حساب اتجاه النظر
            updateLookDirection()
        }

    func updateLookDirection
        # تحويل الزوايا لاتجاه
        nYawRad = nCameraYaw * 3.14159 / 180.0
        nPitchRad = nCameraPitch * 3.14159 / 180.0
        
        aLookDirection = [
            cos(nPitchRad) * sin(nYawRad),
            sin(nPitchRad),
            cos(nPitchRad) * cos(nYawRad)
        ]

    func updateMovement nDeltaTime
        if oGameObject = null {
            return
        }
        
        # حساب السرعة الحالية
        nCurrentSpeed = nMovementSpeed
        if bIsRunning {
            nCurrentSpeed = nRunSpeed
        }
        
        # تحويل اتجاه الحركة للإحداثيات العالمية
        nYawRad = nCameraYaw * 3.14159 / 180.0
        
        aWorldMovement = [
            aMovementDirection[1] * cos(nYawRad) - aMovementDirection[3] * sin(nYawRad),
            0,
            aMovementDirection[1] * sin(nYawRad) + aMovementDirection[3] * cos(nYawRad)
        ]
        
        # تطبيق الحركة
        if oRigidBody != null {
            # استخدام الفيزياء للحركة
            aCurrentVelocity = oRigidBody.getVelocity()
            
            aNewVelocity = [
                aWorldMovement[1] * nCurrentSpeed,
                aCurrentVelocity[2],  # الحفاظ على السرعة العمودية
                aWorldMovement[3] * nCurrentSpeed
            ]
            
            oRigidBody.setVelocity(aNewVelocity)
        else
            # حركة مباشرة بدون فيزياء
            aCurrentPos = oGameObject.getPosition()
            aNewPos = [
                aCurrentPos[1] + aWorldMovement[1] * nCurrentSpeed * nDeltaTime,
                aCurrentPos[2],
                aCurrentPos[3] + aWorldMovement[3] * nCurrentSpeed * nDeltaTime
            ]
            oGameObject.setPosition(aNewPos)
        }
        
        # تحديث دوران الكائن ليواجه اتجاه الحركة
        if aWorldMovement[1] != 0 or aWorldMovement[3] != 0 {
            nTargetYaw = atan2(aWorldMovement[1], aWorldMovement[3]) * 180.0 / 3.14159
            aCurrentRotation = oGameObject.getRotation()
            oGameObject.setRotation([aCurrentRotation[1], nTargetYaw, aCurrentRotation[3]])
        }

    func jump
        if not bIsGrounded or oRigidBody = null {
            return
        }
        
        # حساب قوة القفز
        nJumpForce = sqrt(2 * 9.81 * nJumpHeight)
        
        # تطبيق قوة القفز
        oRigidBody.addForce([0, nJumpForce * oRigidBody.getMass(), 0])
        
        bIsJumping = true
        bIsGrounded = false

    func checkGrounded
        # فحص إذا كان اللاعب على الأرض
        if oGameObject = null or oCollider = null {
            return
        }
        
        # إرسال شعاع للأسفل للتحقق من الأرضية
        aPosition = oGameObject.getPosition()
        aRayStart = [aPosition[1], aPosition[2], aPosition[3]]
        aRayEnd = [aPosition[1], aPosition[2] - 0.6, aPosition[3]]
        
        # هذا يتطلب نظام raycasting في محرك الفيزياء
        # للآن سنستخدم فحص بسيط
        bWasGrounded = bIsGrounded
        
        # فحص بسيط - إذا كان اللاعب قريب من الأرض
        if aPosition[2] <= 1.1 {  # ارتفاع اللاعب + هامش صغير
            bIsGrounded = true
            if bIsJumping {
                bIsJumping = false
            }
        else
            bIsGrounded = false
        }
        
        # إشعار عند الهبوط
        if not bWasGrounded and bIsGrounded {
            onLanded()
        }

    func onLanded
        # استدعى عند هبوط اللاعب على الأرض
        ? "اللاعب هبط على الأرض"

    func getCameraYaw
        return nCameraYaw

    func getCameraPitch
        return nCameraPitch

    func getLookDirection
        return aLookDirection

    func getMovementDirection
        return aMovementDirection

    func setPosition aPosition
        if oGameObject != null {
            oGameObject.setPosition(aPosition)
        }

    func teleport aPosition
        # نقل فوري للاعب
        setPosition(aPosition)
        if oRigidBody != null {
            oRigidBody.setVelocity([0, 0, 0])
        }

    func enableMovement
        bCanMove = true

    func disableMovement
        bCanMove = false
        if oRigidBody != null {
            oRigidBody.setVelocity([0, 0, 0])
        }

    private
        nMovementSpeed
        nRunSpeed
        nJumpHeight
        nMouseSensitivity
        bIsGrounded
        bIsRunning
        bIsJumping
        bCanMove
        aMovementDirection
        aLookDirection
        nCameraPitch
        nCameraYaw
        nMaxPitch
        oRigidBody
        oCollider
        cMoveForward
        cMoveBackward
        cMoveLeft
        cMoveRight
        cJump
        cRun
        cCrouch
}
